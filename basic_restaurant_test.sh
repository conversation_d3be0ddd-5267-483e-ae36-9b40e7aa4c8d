#!/bin/bash

# Basic Restaurant Management API Test Script
# Simple and reliable testing without complex parsing

set -e

# Configuration
BASE_URL="http://localhost:8080/api/v1"
AUTH_URL="$BASE_URL/auth"
RESTAURANT_URL="$BASE_URL/restaurants"
MENU_URL="$BASE_URL/menus"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🍽️ Restaurant Management API Test${NC}"
echo "Base URL: $BASE_URL"
echo "Started: $(date)"
echo

# Simple test function
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    local description=$5
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "  Method: $method"
    echo "  URL: $url"
    
    if [ -n "$data" ]; then
        echo "  Request Body:"
        echo "$data" | jq '.' 2>/dev/null || echo "$data"
    fi
    
    # Make request
    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" \
                -H "Authorization: Bearer $token" "$url")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" "$url")
        fi
    fi
    
    # Extract status code
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    echo "  Response Code: $http_code"
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo -e "  ${GREEN}✅ SUCCESS${NC}"
        if [ -n "$body" ]; then
            echo "  Response:"
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        fi
        echo "$body"
    else
        echo -e "  ${RED}❌ FAILED${NC}"
        echo "  Error Response:"
        echo "$body"
        return 1
    fi
    
    echo
}

# 1. Create Restaurant Owner
echo -e "${YELLOW}=== Step 1: Create Restaurant Owner ===${NC}"
SIGNUP_DATA='{
    "username": "restowner",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Restaurant",
    "lastName": "Owner",
    "roles": ["restaurant"]
}'

response=$(test_api "POST" "$AUTH_URL/signup" "$SIGNUP_DATA" "" "Create restaurant owner")

# 2. Sign in Restaurant Owner
echo -e "${YELLOW}=== Step 2: Sign in Restaurant Owner ===${NC}"
SIGNIN_DATA='{
    "usernameOrEmail": "restowner",
    "password": "password123"
}'

response=$(test_api "POST" "$AUTH_URL/signin" "$SIGNIN_DATA" "" "Sign in restaurant owner")
RESTAURANT_TOKEN=$(echo "$response" | jq -r '.accessToken' 2>/dev/null || echo "")

if [ -z "$RESTAURANT_TOKEN" ] || [ "$RESTAURANT_TOKEN" = "null" ]; then
    echo -e "${RED}Failed to get restaurant token${NC}"
    exit 1
fi

echo -e "${GREEN}Restaurant token obtained: ${RESTAURANT_TOKEN:0:30}...${NC}"
echo

# 3. Create Chef
echo -e "${YELLOW}=== Step 3: Create Chef ===${NC}"
CHEF_SIGNUP_DATA='{
    "username": "chefuser",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Chef",
    "lastName": "User",
    "roles": ["chef"]
}'

response=$(test_api "POST" "$AUTH_URL/signup" "$CHEF_SIGNUP_DATA" "" "Create chef")

# 4. Sign in Chef
echo -e "${YELLOW}=== Step 4: Sign in Chef ===${NC}"
CHEF_SIGNIN_DATA='{
    "usernameOrEmail": "chefuser",
    "password": "password123"
}'

response=$(test_api "POST" "$AUTH_URL/signin" "$CHEF_SIGNIN_DATA" "" "Sign in chef")
CHEF_TOKEN=$(echo "$response" | jq -r '.accessToken' 2>/dev/null || echo "")

if [ -z "$CHEF_TOKEN" ] || [ "$CHEF_TOKEN" = "null" ]; then
    echo -e "${RED}Failed to get chef token${NC}"
    exit 1
fi

echo -e "${GREEN}Chef token obtained: ${CHEF_TOKEN:0:30}...${NC}"
echo

# 5. Create Restaurant
echo -e "${YELLOW}=== Step 5: Create Restaurant ===${NC}"
RESTAURANT_DATA='{
    "name": "Test Restaurant API",
    "description": "A test restaurant for API testing",
    "address": "123 Test Street",
    "city": "Test City",
    "state": "Test State",
    "postalCode": "12345",
    "country": "Test Country",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "phoneNumber": "+1234567892",
    "email": "<EMAIL>",
    "deliveryFee": 5.99,
    "minimumOrder": 15.00,
    "estimatedDeliveryTime": 30,
    "deliveryRadius": 5.0,
    "cuisineTypes": ["Italian", "Pizza"],
    "specialFeatures": ["Outdoor Seating", "Delivery"]
}'

response=$(test_api "POST" "$RESTAURANT_URL" "$RESTAURANT_DATA" "$RESTAURANT_TOKEN" "Create restaurant")
RESTAURANT_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$RESTAURANT_ID" ] || [ "$RESTAURANT_ID" = "null" ]; then
    echo -e "${RED}Failed to get restaurant ID${NC}"
    exit 1
fi

echo -e "${GREEN}Restaurant created with ID: $RESTAURANT_ID${NC}"
echo

# 6. Test Restaurant Access
echo -e "${YELLOW}=== Step 6: Test Restaurant Access ===${NC}"
test_api "GET" "$RESTAURANT_URL/$RESTAURANT_ID" "" "" "Get restaurant (public)"
test_api "GET" "$RESTAURANT_URL/my-restaurant" "" "$RESTAURANT_TOKEN" "Get my restaurant (owner)"

# 7. Create Menu Category
echo -e "${YELLOW}=== Step 7: Create Menu Category ===${NC}"
CATEGORY_DATA='{
    "name": "Appetizers",
    "description": "Delicious appetizers",
    "sortOrder": 1,
    "isActive": true
}'

response=$(test_api "POST" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "$CATEGORY_DATA" "$RESTAURANT_TOKEN" "Create menu category")
CATEGORY_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$CATEGORY_ID" ] || [ "$CATEGORY_ID" = "null" ]; then
    echo -e "${RED}Failed to get category ID${NC}"
    exit 1
fi

echo -e "${GREEN}Category created with ID: $CATEGORY_ID${NC}"
echo

# 8. Create Menu Item
echo -e "${YELLOW}=== Step 8: Create Menu Item ===${NC}"
ITEM_DATA='{
    "name": "Bruschetta",
    "description": "Fresh tomatoes and basil",
    "price": 8.99,
    "isAvailable": true,
    "isFeatured": true,
    "isVegetarian": true,
    "preparationTime": 10,
    "ingredients": ["Tomatoes", "Basil", "Bread"],
    "allergens": ["Gluten"]
}'

response=$(test_api "POST" "$MENU_URL/categories/$CATEGORY_ID/items" "$ITEM_DATA" "$RESTAURANT_TOKEN" "Create menu item")
ITEM_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$ITEM_ID" ] || [ "$ITEM_ID" = "null" ]; then
    echo -e "${RED}Failed to get item ID${NC}"
    exit 1
fi

echo -e "${GREEN}Menu item created with ID: $ITEM_ID${NC}"
echo

# 9. Test Public Menu Access
echo -e "${YELLOW}=== Step 9: Test Public Menu Access ===${NC}"
test_api "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "" "Get restaurant categories (public)"
test_api "GET" "$MENU_URL/categories/$CATEGORY_ID/items" "" "" "Get category items (public)"

# 10. Test Restaurant Search
echo -e "${YELLOW}=== Step 10: Test Restaurant Search ===${NC}"
test_api "GET" "$RESTAURANT_URL/search?query=Test&page=0&size=5" "" "" "Search restaurants"
test_api "GET" "$RESTAURANT_URL?page=0&size=5" "" "" "Get all restaurants"

echo -e "${GREEN}🎉 All Basic Tests Completed Successfully! 🎉${NC}"
echo
echo -e "${BLUE}Summary:${NC}"
echo "✅ Restaurant owner created and authenticated"
echo "✅ Chef created and authenticated"
echo "✅ Restaurant created successfully"
echo "✅ Menu category and item created"
echo "✅ Public endpoints tested"
echo
echo "Generated IDs:"
echo "  Restaurant ID: $RESTAURANT_ID"
echo "  Category ID: $CATEGORY_ID"
echo "  Item ID: $ITEM_ID"
