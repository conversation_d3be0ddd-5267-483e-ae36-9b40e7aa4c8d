# 🍽️ Restaurant Management API Test Suite

## Overview
This comprehensive test suite validates the Restaurant Management System APIs with detailed logging and comprehensive coverage.

## Features Tested

### 🔐 Authentication & Authorization
- ✅ Restaurant owner account creation
- ✅ Chef account creation  
- ✅ JWT token authentication
- ✅ Role-based access control

### 🏪 Restaurant Management
- ✅ Restaurant profile creation
- ✅ Restaurant information retrieval
- ✅ Restaurant status management
- ✅ Owner access control

### 📋 Menu Management
- ✅ Menu category creation
- ✅ Menu item creation with full details
- ✅ Menu item updates
- ✅ Menu retrieval (public access)

### 👨‍🍳 Chef System
- ✅ Chef-restaurant association
- ✅ Chef access to restaurant management
- ✅ Independent chef accounts (not tied to restaurants)

### 🔍 Search & Discovery
- ✅ Restaurant search by name
- ✅ Nearby restaurant discovery
- ✅ Featured restaurants
- ✅ Top-rated restaurants

## Test Scripts

### 1. Enhanced Test Script: `simple_restaurant_test.sh`
**Comprehensive test with detailed logging**

```bash
./simple_restaurant_test.sh
```

**Features:**
- 📊 Detailed request/response logging
- 🎨 Color-coded output
- ⏱️ Response time measurement
- 📝 Automatic log file generation
- 📈 Test statistics and summary
- 🔍 JSON response formatting

### 2. Full Test Script: `test_restaurant_endpoints.sh`
**Complete endpoint coverage**

```bash
./test_restaurant_endpoints.sh
```

## Test Flow

### Step 1-2: Authentication Setup
1. **Create Restaurant Owner** - `POST /api/auth/signup`
2. **Authenticate Owner** - `POST /api/auth/signin`

### Step 3-4: Chef Setup  
3. **Create Chef Account** - `POST /api/auth/signup`
4. **Authenticate Chef** - `POST /api/auth/signin`

### Step 5-6: Restaurant Management
5. **Create Restaurant** - `POST /api/restaurants`
6. **Test Access Control** - `GET /api/restaurants/{id}`

### Step 7-8: Menu Management
7. **Create Menu Category** - `POST /api/menus/restaurants/{id}/categories`
8. **Create Menu Item** - `POST /api/menus/categories/{id}/items`

### Step 9-11: Chef Association
9. **Test Unauthorized Chef Access** - Should fail
10. **Associate Chef with Restaurant** - `POST /api/restaurants/{id}/chefs/{chefId}`
11. **Test Authorized Chef Access** - Should succeed

### Step 12-14: Public APIs & Management
12. **Test Public Menu Access** - `GET /api/menus/restaurants/{id}/categories`
13. **Test Search & Discovery** - Various search endpoints
14. **Test Restaurant Management** - Status toggles, etc.

## Sample Output

```
╔══════════════════════════════════════════════════════════════╗
║           🍽️  Restaurant Management API Test Suite           ║
╚══════════════════════════════════════════════════════════════╝
📍 Base URL: http://localhost:8080/api
📝 Log File: restaurant_api_test_20241202_143022.log
🕐 Started: Sun Dec  2 14:30:22 PST 2024

┌─────────────────────────────────────────────────────────────┐
│ Test #1: Create restaurant owner account
└─────────────────────────────────────────────────────────────┘
📤 REQUEST DETAILS:
   Method: POST
   URL: http://localhost:8080/api/auth/signup
   Authorization: None
   Request Body:
{
  "username": "restaurant_test",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Restaurant",
  "lastName": "Owner",
  "roles": ["restaurant"]
}

📥 RESPONSE DETAILS:
   Status Code: 201
   Response Time: 0.234s
   Response Body:
{
  "message": "User registered successfully!",
  "username": "restaurant_test"
}

✅ TEST PASSED - HTTP 201
```

## Log Files

Each test run generates a detailed log file with timestamp:
- **Format**: `restaurant_api_test_YYYYMMDD_HHMMSS.log`
- **Content**: Complete request/response details, timing, and results

## Prerequisites

1. **Server Running**: Ensure ReelSnack server is running on `localhost:8080`
2. **Dependencies**: `curl`, `jq` (for JSON formatting)
3. **Database**: PostgreSQL database should be accessible

## Configuration

Edit the script to modify:
```bash
BASE_URL="http://localhost:8080/api"  # Change if different port/host
```

## Expected Results

### ✅ Successful Test Run
- All authentication flows work
- Restaurant creation succeeds
- Menu management works
- Chef association functions properly
- Public APIs are accessible

### ❌ Common Issues
1. **Server not running** - Check if Spring Boot app is started
2. **Database connection** - Verify PostgreSQL is running
3. **Port conflicts** - Ensure port 8080 is available
4. **Migration issues** - Run migrations if database schema is outdated

## API Endpoints Tested

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/signup` | Create user account | No |
| POST | `/auth/signin` | Authenticate user | No |
| POST | `/restaurants` | Create restaurant | Yes (Owner/Chef) |
| GET | `/restaurants/{id}` | Get restaurant | No |
| GET | `/restaurants/my-restaurant` | Get owned restaurant | Yes (Owner/Chef) |
| POST | `/restaurants/{id}/chefs/{chefId}` | Associate chef | Yes (Owner) |
| POST | `/menus/restaurants/{id}/categories` | Create category | Yes (Owner/Chef) |
| POST | `/menus/categories/{id}/items` | Create menu item | Yes (Owner/Chef) |
| PUT | `/menus/items/{id}` | Update menu item | Yes (Owner/Chef) |
| GET | `/menus/restaurants/{id}/categories` | Get categories | No |
| GET | `/restaurants/search` | Search restaurants | No |
| GET | `/restaurants/nearby` | Find nearby | No |
| PATCH | `/restaurants/{id}/toggle-status` | Toggle status | Yes (Owner/Chef) |

## Next Steps

After successful tests:
1. **Phase 3**: Order Management System
2. **Payment Integration**: Paystack integration
3. **Real-time Features**: Order tracking
4. **Analytics**: Restaurant dashboards

---

**🎯 This test suite ensures the Restaurant Management System is production-ready!**
