package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.response.FollowStatsResponse;
import com.reelsnack.reelsnack.dto.response.UserFollowResponse;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.ContentRepository;
import com.reelsnack.reelsnack.repository.UserFollowRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserFollowServiceMutualFollowersTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserFollowRepository userFollowRepository;

    @Mock
    private ContentRepository contentRepository;

    @InjectMocks
    private UserFollowService userFollowService;

    private User user1;
    private User user2;
    private User currentUser;
    private User mutualFollower1;
    private User mutualFollower2;

    @BeforeEach
    void setUp() {
        // Create test users
        user1 = User.builder()
                .id(1L)
                .username("chef_maria")
                .email("<EMAIL>")
                .firstName("Maria")
                .lastName("Rodriguez")
                .password("password")
                .role(Role.ROLE_CHEF)
                .enabled(true)
                .emailVerified(true)
                .build();

        user2 = User.builder()
                .id(2L)
                .username("foodie_john")
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();

        currentUser = User.builder()
                .id(3L)
                .username("current_user")
                .email("<EMAIL>")
                .firstName("Current")
                .lastName("User")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();

        mutualFollower1 = User.builder()
                .id(4L)
                .username("mutual_follower1")
                .email("<EMAIL>")
                .firstName("Mutual")
                .lastName("Follower1")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();

        mutualFollower2 = User.builder()
                .id(5L)
                .username("mutual_follower2")
                .email("<EMAIL>")
                .firstName("Mutual")
                .lastName("Follower2")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();
    }

    @Test
    void getMutualFollowers_Success() {
        // Given
        List<User> mutualFollowers = Arrays.asList(mutualFollower1, mutualFollower2);
        Page<User> mutualFollowersPage = new PageImpl<>(mutualFollowers);
        Pageable pageable = PageRequest.of(0, 10);

        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        when(userRepository.findByUsername("foodie_john")).thenReturn(Optional.of(user2));
        when(userFollowRepository.findMutualFollowers(user1, user2, pageable)).thenReturn(mutualFollowersPage);
        
        // Mock follow status checks for each mutual follower
        when(userFollowRepository.existsByFollowerUsernameAndFollowingUsername("current_user", "mutual_follower1"))
                .thenReturn(true);
        when(userFollowRepository.existsByFollowerUsernameAndFollowingUsername("current_user", "mutual_follower2"))
                .thenReturn(false);

        // When
        Page<UserFollowResponse.UserSummaryResponse> result = userFollowService.getMutualFollowers(
                "chef_maria", "foodie_john", pageable, "current_user");

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        
        UserFollowResponse.UserSummaryResponse firstMutual = result.getContent().get(0);
        assertEquals("mutual_follower1", firstMutual.getUsername());
        assertEquals("Mutual", firstMutual.getFirstName());
        assertEquals("Follower1", firstMutual.getLastName());
        assertTrue(firstMutual.getIsFollowedByCurrentUser());

        UserFollowResponse.UserSummaryResponse secondMutual = result.getContent().get(1);
        assertEquals("mutual_follower2", secondMutual.getUsername());
        assertFalse(secondMutual.getIsFollowedByCurrentUser());

        verify(userRepository).findByUsername("chef_maria");
        verify(userRepository).findByUsername("foodie_john");
        verify(userFollowRepository).findMutualFollowers(user1, user2, pageable);
    }

    @Test
    void getMutualFollowers_User1NotFound() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> userFollowService.getMutualFollowers("chef_maria", "foodie_john", pageable, "current_user"));
    }

    @Test
    void getMutualFollowers_User2NotFound() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        when(userRepository.findByUsername("foodie_john")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> userFollowService.getMutualFollowers("chef_maria", "foodie_john", pageable, "current_user"));
    }

    @Test
    void getMutualFollowers_NoCurrentUser() {
        // Given
        List<User> mutualFollowers = Arrays.asList(mutualFollower1);
        Page<User> mutualFollowersPage = new PageImpl<>(mutualFollowers);
        Pageable pageable = PageRequest.of(0, 10);

        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        when(userRepository.findByUsername("foodie_john")).thenReturn(Optional.of(user2));
        when(userFollowRepository.findMutualFollowers(user1, user2, pageable)).thenReturn(mutualFollowersPage);

        // When
        Page<UserFollowResponse.UserSummaryResponse> result = userFollowService.getMutualFollowers(
                "chef_maria", "foodie_john", pageable, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        
        UserFollowResponse.UserSummaryResponse mutual = result.getContent().get(0);
        assertEquals("mutual_follower1", mutual.getUsername());
        assertFalse(mutual.getIsFollowedByCurrentUser()); // Should be false when no current user
    }

    @Test
    void getFollowStats_WithMutualFollowersCount() {
        // Given
        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        when(userRepository.findByUsername("current_user")).thenReturn(Optional.of(currentUser));
        
        // Mock follow counts
        when(userFollowRepository.countByFollowing(user1)).thenReturn(150L);
        when(userFollowRepository.countByFollower(user1)).thenReturn(1200L);
        when(contentRepository.countByCreatorAndIsPublicTrueAndIsDeletedFalse(user1)).thenReturn(25L);
        
        // Mock relationship checks
        when(userFollowRepository.existsByFollowerUsernameAndFollowingUsername("current_user", "chef_maria"))
                .thenReturn(true);
        when(userFollowRepository.existsByFollowerUsernameAndFollowingUsername("chef_maria", "current_user"))
                .thenReturn(false);
        
        // Mock mutual followers count
        when(userFollowRepository.countMutualFollowers(user1, currentUser)).thenReturn(15L);

        // When
        FollowStatsResponse result = userFollowService.getFollowStats("chef_maria", "current_user");

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getUserId());
        assertEquals("chef_maria", result.getUsername());
        assertEquals(1200L, result.getFollowerCount());
        assertEquals(150L, result.getFollowingCount());
        assertEquals(25L, result.getContentCount());
        assertTrue(result.getIsFollowedByCurrentUser());
        assertFalse(result.getFollowsCurrentUser());
        assertEquals(15L, result.getMutualFollowersCount());

        verify(userFollowRepository).countMutualFollowers(user1, currentUser);
    }

    @Test
    void getFollowStats_SameUser_NoMutualFollowersCount() {
        // Given
        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        
        // Mock follow counts
        when(userFollowRepository.countByFollowing(user1)).thenReturn(150L);
        when(userFollowRepository.countByFollower(user1)).thenReturn(1200L);
        when(contentRepository.countByCreatorAndIsPublicTrueAndIsDeletedFalse(user1)).thenReturn(25L);

        // When
        FollowStatsResponse result = userFollowService.getFollowStats("chef_maria", "chef_maria");

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getMutualFollowersCount()); // Should be 0 for same user
        assertFalse(result.getIsFollowedByCurrentUser());
        assertFalse(result.getFollowsCurrentUser());

        verify(userFollowRepository, never()).countMutualFollowers(any(), any());
    }

    @Test
    void getFollowStats_NoCurrentUser_NoMutualFollowersCount() {
        // Given
        when(userRepository.findByUsername("chef_maria")).thenReturn(Optional.of(user1));
        
        // Mock follow counts
        when(userFollowRepository.countByFollowing(user1)).thenReturn(150L);
        when(userFollowRepository.countByFollower(user1)).thenReturn(1200L);
        when(contentRepository.countByCreatorAndIsPublicTrueAndIsDeletedFalse(user1)).thenReturn(25L);

        // When
        FollowStatsResponse result = userFollowService.getFollowStats("chef_maria", null);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getMutualFollowersCount()); // Should be 0 when no current user
        assertFalse(result.getIsFollowedByCurrentUser());
        assertFalse(result.getFollowsCurrentUser());

        verify(userFollowRepository, never()).countMutualFollowers(any(), any());
    }
}
