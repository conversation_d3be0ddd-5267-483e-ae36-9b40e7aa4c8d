package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.config.properties.ContentProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ThumbnailGenerationServiceTest {

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private ContentProperties contentProperties;

    @Mock
    private FallbackThumbnailService fallbackThumbnailService;

    @InjectMocks
    private ThumbnailGenerationService thumbnailGenerationService;

    private MockMultipartFile imageFile;
    private MockMultipartFile videoFile;
    private MockMultipartFile unsupportedFile;

    @BeforeEach
    void setUp() throws IOException {
        // Create a simple test image
        BufferedImage testImage = new BufferedImage(640, 480, BufferedImage.TYPE_INT_RGB);
        ByteArrayOutputStream imageStream = new ByteArrayOutputStream();
        ImageIO.write(testImage, "jpg", imageStream);
        
        imageFile = new MockMultipartFile(
            "image",
            "test-image.jpg",
            "image/jpeg",
            imageStream.toByteArray()
        );

        // Create a mock video file (we can't create real video content easily)
        videoFile = new MockMultipartFile(
            "video",
            "test-video.mp4",
            "video/mp4",
            "fake video content".getBytes()
        );

        // Create an unsupported file
        unsupportedFile = new MockMultipartFile(
            "document",
            "test-document.pdf",
            "application/pdf",
            "fake pdf content".getBytes()
        );
    }

    @Test
    void generateThumbnail_ImageFile_Success() {
        // Given
        when(fileStorageService.storeFile(any(MultipartFile.class), eq("thumbnails")))
            .thenReturn("thumbnails/generated-thumbnail.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(imageFile, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/generated-thumbnail.jpg", result);
        verify(fileStorageService).storeFile(any(MultipartFile.class), eq("thumbnails"));
    }

    @Test
    void generateThumbnail_VideoFile_FallbackOnError() {
        // Given - Video processing will fail due to fake content
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(videoFile, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-recipe.jpg", result);
        // Should not call fileStorageService since video processing fails
        verify(fileStorageService, never()).storeFile(any(MultipartFile.class), eq("thumbnails"));
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("RECIPE_VIDEO");
    }

    @Test
    void generateThumbnail_UnsupportedFile_ReturnsFallback() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl("TUTORIAL"))
            .thenReturn("thumbnails/fallback-tutorial.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(unsupportedFile, "TUTORIAL");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-tutorial.jpg", result);
        verify(fileStorageService, never()).storeFile(any(MultipartFile.class), eq("thumbnails"));
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("TUTORIAL");
    }

    @Test
    void generateThumbnail_NullFile_ReturnsFallback() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(null, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-recipe.jpg", result);
        verify(fileStorageService, never()).storeFile(any(MultipartFile.class), eq("thumbnails"));
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("RECIPE_VIDEO");
    }

    @Test
    void generateThumbnail_EmptyFile_ReturnsFallback() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
            "empty",
            "empty.jpg",
            "image/jpeg",
            new byte[0]
        );
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(emptyFile, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-recipe.jpg", result);
        verify(fileStorageService, never()).storeFile(any(MultipartFile.class), eq("thumbnails"));
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("RECIPE_VIDEO");
    }

    @Test
    void generateThumbnail_FileStorageError_ReturnsFallback() {
        // Given
        when(fileStorageService.storeFile(any(MultipartFile.class), eq("thumbnails")))
            .thenThrow(new RuntimeException("Storage error"));
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(imageFile, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-recipe.jpg", result);
        verify(fileStorageService).storeFile(any(MultipartFile.class), eq("thumbnails"));
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("RECIPE_VIDEO");
    }

    @Test
    void generateThumbnail_DifferentContentTypes_CorrectFallbacks() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RESTAURANT_PROMO"))
            .thenReturn("thumbnails/fallback-restaurant.jpg");
        when(fallbackThumbnailService.getFallbackThumbnailUrl("TUTORIAL"))
            .thenReturn("thumbnails/fallback-tutorial.jpg");
        when(fallbackThumbnailService.getFallbackThumbnailUrl("REVIEW"))
            .thenReturn("thumbnails/fallback-review.jpg");
        when(fallbackThumbnailService.getFallbackThumbnailUrl("UNKNOWN_TYPE"))
            .thenReturn("thumbnails/fallback-default.jpg");

        // When & Then
        String recipeResult = thumbnailGenerationService.generateThumbnail(null, "RECIPE_VIDEO");
        assertEquals("thumbnails/fallback-recipe.jpg", recipeResult);

        String restaurantResult = thumbnailGenerationService.generateThumbnail(null, "RESTAURANT_PROMO");
        assertEquals("thumbnails/fallback-restaurant.jpg", restaurantResult);

        String tutorialResult = thumbnailGenerationService.generateThumbnail(null, "TUTORIAL");
        assertEquals("thumbnails/fallback-tutorial.jpg", tutorialResult);

        String reviewResult = thumbnailGenerationService.generateThumbnail(null, "REVIEW");
        assertEquals("thumbnails/fallback-review.jpg", reviewResult);

        String unknownResult = thumbnailGenerationService.generateThumbnail(null, "UNKNOWN_TYPE");
        assertEquals("thumbnails/fallback-default.jpg", unknownResult);
    }

    @Test
    void generateThumbnail_SupportedImageFormats() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // Test various supported image formats
        String[] supportedFormats = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

        for (String format : supportedFormats) {
            MockMultipartFile file = new MockMultipartFile(
                "image",
                "test." + format,
                "image/" + format,
                "fake image content".getBytes()
            );

            // Should not throw exception and should return fallback (since content is fake)
            String result = thumbnailGenerationService.generateThumbnail(file, "RECIPE_VIDEO");
            assertNotNull(result);
            assertEquals("thumbnails/fallback-recipe.jpg", result);
        }
    }

    @Test
    void generateThumbnail_SupportedVideoFormats() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // Test various supported video formats
        String[] supportedFormats = {"mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "m4v"};

        for (String format : supportedFormats) {
            MockMultipartFile file = new MockMultipartFile(
                "video",
                "test." + format,
                "video/" + format,
                "fake video content".getBytes()
            );

            // Should not throw exception and should return fallback (since content is fake)
            String result = thumbnailGenerationService.generateThumbnail(file, "RECIPE_VIDEO");
            assertNotNull(result);
            assertEquals("thumbnails/fallback-recipe.jpg", result);
        }
    }

    @Test
    void generateThumbnail_ImageProcessing_OptimalDimensions() {
        // Given
        when(fileStorageService.storeFile(any(MultipartFile.class), eq("thumbnails")))
            .thenReturn("thumbnails/optimized-thumbnail.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(imageFile, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/optimized-thumbnail.jpg", result);
        
        // Verify that the stored file is properly sized (we can't easily test the actual dimensions
        // without more complex setup, but we can verify the service was called)
        verify(fileStorageService).storeFile(any(MultipartFile.class), eq("thumbnails"));
    }

    @Test
    void generateThumbnail_NoFileExtension_HandledGracefully() {
        // Given
        MockMultipartFile fileWithoutExtension = new MockMultipartFile(
            "file",
            "filename_without_extension",
            "application/octet-stream",
            "some content".getBytes()
        );
        when(fallbackThumbnailService.getFallbackThumbnailUrl("RECIPE_VIDEO"))
            .thenReturn("thumbnails/fallback-recipe.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(fileWithoutExtension, "RECIPE_VIDEO");

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-recipe.jpg", result);
        verify(fallbackThumbnailService).getFallbackThumbnailUrl("RECIPE_VIDEO");
    }

    @Test
    void generateThumbnail_NullContentType_UsesDefault() {
        // Given
        when(fallbackThumbnailService.getFallbackThumbnailUrl(null))
            .thenReturn("thumbnails/fallback-default.jpg");

        // When
        String result = thumbnailGenerationService.generateThumbnail(null, null);

        // Then
        assertNotNull(result);
        assertEquals("thumbnails/fallback-default.jpg", result);
        verify(fallbackThumbnailService).getFallbackThumbnailUrl(null);
    }
}
