package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.CreateContentRequest;
import com.reelsnack.reelsnack.dto.response.ContentResponse;
import com.reelsnack.reelsnack.dto.response.UserProfileResponse;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.mapper.ContentMapper;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.ContentRepository;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ContentServiceTest {

    @Mock
    private ContentRepository contentRepository;

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private VideoProcessingService videoProcessingService;

    @Mock
    private ThumbnailGenerationService thumbnailGenerationService;

    @Mock
    private ContentMapper contentMapper;

    @Mock
    private UserService userService;

    @Mock
    private UserMapper userMapper;

    private ContentService contentService;

    private User testUser;
    private UserDetailsImpl userDetails;
    private Content testContent;
    private ContentResponse testContentResponse;
    private CreateContentRequest createContentRequest;
    private MultipartFile testFile;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        contentService = new ContentService(
            contentRepository,
            userService,
            fileStorageService,
            videoProcessingService,
            contentMapper,
            userMapper
        );

        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .password("password")
                .role(Role.ROLE_USER)
                .build();

        userDetails = UserDetailsImpl.build(testUser);
        
        // Initialize test content
        testContent = Content.builder()
                .id(1L)
                .title("Test Content")
                .description("Test Description")
                .contentType(ContentType.TUTORIAL)
                .creator(testUser)
                .isPublic(true)
                .build();
                
        // Mock user mapper behavior
        UserProfileResponse userProfileResponse = UserProfileResponse.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .followerCount(0)
                .followingCount(0)
                .isFollowed(false)
                .build();
                
        testContentResponse = ContentResponse.builder()
                .id(1L)
                .title("Test Content")
                .description("Test Description")
                .contentType(ContentType.TUTORIAL)
                .creator(userProfileResponse)
                .isPublic(true)
                .build();

        createContentRequest = new CreateContentRequest();
        createContentRequest.setTitle("Test Content");
        createContentRequest.setDescription("Test Description");
        createContentRequest.setContentType(ContentType.TUTORIAL);

        testFile = new MockMultipartFile(
                "file",
                "test.mp4",
                "video/mp4",
                "Test Video Content".getBytes()
        );
    }

    @Test
    void createContent_ValidRequest_ReturnsContentResponse() throws IOException {
        // Arrange
        when(fileStorageService.storeFile(any(MultipartFile.class), anyString())).thenReturn("videos/test.mp4");
        when(contentRepository.save(any(Content.class))).thenAnswer(invocation -> {
            Content content = invocation.getArgument(0);
            content.setId(1L);
            return content;
        });
        when(userService.findByUsername(anyString())).thenReturn(testUser);
        
        // Mock video processing result
        VideoProcessingService.VideoProcessingResult videoResult = VideoProcessingService.VideoProcessingResult.builder()
            .thumbnailUrl("videos/thumbnail.jpg")
            .duration(60) // in seconds
            .format("mp4")
            .size(1024 * 1024) // 1MB
            .width(1280)
            .height(720)
            .build();
        when(videoProcessingService.processVideo(any(MultipartFile.class))).thenReturn(videoResult);

        // Act
        ContentResponse response = contentService.createContent(createContentRequest, testFile, userDetails);

        // Assert
        assertNotNull(response);
        assertEquals("Test Content", response.getTitle());
        assertEquals("Test Description", response.getDescription());
        assertEquals(ContentType.TUTORIAL, response.getContentType());

        verify(contentRepository, times(1)).save(any(Content.class));
        verify(fileStorageService, times(1)).storeFile(any(MultipartFile.class), anyString());
        verify(videoProcessingService, times(1)).processVideo(any(MultipartFile.class));
        verify(userService, times(1)).findByUsername(anyString());
    }

    @Test
    void getContentById_ExistingId_ReturnsContent() {
        // Arrange
        testContent.setIsPublic(true); // Make sure content is public
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(contentRepository.save(any(Content.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // Setup userMapper mock for toUserProfileResponse
        when(userMapper.toUserProfileResponse(any(User.class))).thenAnswer(invocation -> {
            User user = invocation.getArgument(0);
            UserProfileResponse profile = new UserProfileResponse();
            profile.setId(user.getId());
            profile.setUsername(user.getUsername());
            profile.setEmail(user.getEmail());
            return profile;
        });

        // Act
        ContentResponse response = contentService.getContentById(1L);

        // Assert
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals("Test Content", response.getTitle());
        assertEquals("Test Description", response.getDescription());
        assertEquals(ContentType.TUTORIAL, response.getContentType());
        assertTrue(response.isPublic());
        assertEquals(1, response.getViewCount()); // View count should be incremented

        verify(contentRepository, times(1)).findById(1L);
        verify(contentRepository, times(1)).save(any(Content.class));
        verify(userMapper, times(1)).toUserProfileResponse(any(User.class));
    }

    @Test
    void getContentById_NonExistingId_ThrowsException() {
        // Arrange
        long nonExistingId = 999L;
        when(contentRepository.findById(nonExistingId)).thenReturn(Optional.empty());

        // Act & Assert
        Exception exception = assertThrows(ResourceNotFoundException.class, 
            () -> contentService.getContentById(nonExistingId));
            
        assertEquals("Content not found with id : " + nonExistingId, exception.getMessage());
        verify(contentRepository, times(1)).findById(nonExistingId);
        verifyNoInteractions(contentMapper);
    }

    @Test
    void getContentFeed_ReturnsPaginatedContent() {
        // Arrange
        testContent.setIsPublic(true);
        Pageable pageable = PageRequest.of(0, 10, Sort.by("createdAt").descending());
        Page<Content> contentPage = new PageImpl<>(Collections.singletonList(testContent), pageable, 1);
        
        // Setup the mocks
        when(contentRepository.findByIsPublicTrue(any(Pageable.class))).thenReturn(contentPage);
        when(userMapper.toUserProfileResponse(any(User.class))).thenAnswer(invocation -> {
            User user = invocation.getArgument(0);
            UserProfileResponse profile = new UserProfileResponse();
            profile.setId(user.getId());
            profile.setUsername(user.getUsername());
            profile.setEmail(user.getEmail());
            return profile;
        });

        // Act
        Page<ContentResponse> result = contentService.getContentFeed(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        
        ContentResponse response = result.getContent().get(0);
        assertEquals(1L, response.getId());
        assertEquals("Test Content", response.getTitle());
        assertEquals("Test Description", response.getDescription());
        assertEquals(ContentType.TUTORIAL, response.getContentType());
        assertTrue(response.isPublic());
        assertNotNull(response.getCreator());
        assertEquals("testuser", response.getCreator().getUsername());
        
        // Verify repository and mapper interactions
        verify(contentRepository, times(1)).findByIsPublicTrue(pageable);
        verify(userMapper, times(1)).toUserProfileResponse(any(User.class));
        verifyNoMoreInteractions(contentRepository, contentMapper, userMapper);
    }

    @Test
    void deleteContent_ValidIdAndOwner_DeletesContent() {
        // Arrange
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(contentRepository.save(any(Content.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(userService.findByUsername(anyString())).thenReturn(testUser);

        // Act
        contentService.deleteContent(1L, userDetails);
        
        // Assert
        verify(contentRepository, times(1)).findById(1L);
        
        // Verify content was marked as deleted
        ArgumentCaptor<Content> contentCaptor = ArgumentCaptor.forClass(Content.class);
        verify(contentRepository, times(1)).save(contentCaptor.capture());
        
        Content savedContent = contentCaptor.getValue();
        assertTrue(savedContent.getIsDeleted());
        
        verify(userService, times(1)).findByUsername(userDetails.getUsername());
    }

    @Test
    void deleteContent_NonExistingId_ThrowsException() {
        // Arrange
        long nonExistingId = 999L;
        when(contentRepository.findById(nonExistingId)).thenReturn(Optional.empty());
        when(userService.findByUsername(anyString())).thenReturn(testUser);

        // Act & Assert
        Exception exception = assertThrows(ResourceNotFoundException.class, 
            () -> contentService.deleteContent(nonExistingId, userDetails));
            
        assertEquals("Content not found with id : " + nonExistingId, exception.getMessage());
        
        verify(contentRepository, times(1)).findById(nonExistingId);
        verify(contentRepository, never()).save(any(Content.class));
        verify(userService, times(1)).findByUsername(userDetails.getUsername());
    }
}
