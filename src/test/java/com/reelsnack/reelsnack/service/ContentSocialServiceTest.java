package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.BookmarkContentRequest;
import com.reelsnack.reelsnack.dto.request.ShareContentRequest;
import com.reelsnack.reelsnack.dto.response.BookmarkResponse;
import com.reelsnack.reelsnack.dto.response.ShareResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.model.*;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ContentSocialServiceTest {

    @Mock
    private ContentRepository contentRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private ContentShareRepository contentShareRepository;

    @Mock
    private ContentBookmarkRepository contentBookmarkRepository;

    @InjectMocks
    private ContentSocialService contentSocialService;

    private User testUser;
    private Content testContent;
    private ShareContentRequest shareRequest;
    private BookmarkContentRequest bookmarkRequest;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();

        // Create test content
        testContent = Content.builder()
                .id(1L)
                .title("Test Recipe")
                .description("A delicious test recipe")
                .contentType(ContentType.RECIPE_VIDEO)
                .creator(testUser)
                .isPublic(true)
                .isDeleted(false)
                .viewCount(100L)
                .likeCount(10L)
                .shareCount(5L)
                .commentCount(3L)
                .createdAt(LocalDateTime.now())
                .build();

        // Create test requests
        shareRequest = ShareContentRequest.builder()
                .sharePlatform("FACEBOOK")
                .shareMessage("Check out this amazing recipe!")
                .build();

        bookmarkRequest = BookmarkContentRequest.builder()
                .bookmarkCollection("Favorites")
                .notes("Must try this recipe")
                .build();
    }

    // ===== Content Sharing Tests =====

    @Test
    void shareContent_Success() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentShareRepository.existsByUserAndContent(testUser, testContent)).thenReturn(false);
        
        ContentShare savedShare = ContentShare.builder()
                .id(1L)
                .content(testContent)
                .user(testUser)
                .sharePlatform("FACEBOOK")
                .shareMessage("Check out this amazing recipe!")
                .createdAt(LocalDateTime.now())
                .build();
        
        when(contentShareRepository.save(any(ContentShare.class))).thenReturn(savedShare);
        when(contentRepository.save(any(Content.class))).thenReturn(testContent);

        // When
        ShareResponse response = contentSocialService.shareContent(1L, shareRequest, "testuser");

        // Then
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals(1L, response.getContentId());
        assertEquals("Test Recipe", response.getContentTitle());
        assertEquals("FACEBOOK", response.getSharePlatform());
        assertEquals("Check out this amazing recipe!", response.getShareMessage());
        assertNotNull(response.getSharedBy());
        assertEquals("testuser", response.getSharedBy().getUsername());

        verify(contentRepository).save(testContent);
        verify(contentShareRepository).save(any(ContentShare.class));
    }

    @Test
    void shareContent_ContentNotFound() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> contentSocialService.shareContent(1L, shareRequest, "testuser"));
    }

    @Test
    void shareContent_UserNotFound() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> contentSocialService.shareContent(1L, shareRequest, "testuser"));
    }

    @Test
    void shareContent_PrivateContent() {
        // Given
        testContent.setIsPublic(false);
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // When & Then
        assertThrows(BadRequestException.class, 
                () -> contentSocialService.shareContent(1L, shareRequest, "testuser"));
    }

    @Test
    void getContentShares_Success() {
        // Given
        ContentShare share1 = ContentShare.builder()
                .id(1L)
                .content(testContent)
                .user(testUser)
                .sharePlatform("FACEBOOK")
                .createdAt(LocalDateTime.now())
                .build();

        List<ContentShare> shares = Arrays.asList(share1);
        Page<ContentShare> sharePage = new PageImpl<>(shares);
        Pageable pageable = PageRequest.of(0, 10);

        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(contentShareRepository.findByContent(testContent, pageable)).thenReturn(sharePage);

        // When
        Page<ShareResponse> response = contentSocialService.getContentShares(1L, pageable);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals(1L, response.getContent().get(0).getId());
    }

    // ===== Content Bookmarking Tests =====

    @Test
    void toggleBookmark_AddBookmark_Success() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.existsByUserAndContent(testUser, testContent)).thenReturn(false);
        
        ContentBookmark savedBookmark = ContentBookmark.builder()
                .id(1L)
                .content(testContent)
                .user(testUser)
                .bookmarkCollection("Favorites")
                .notes("Must try this recipe")
                .createdAt(LocalDateTime.now())
                .build();
        
        when(contentBookmarkRepository.save(any(ContentBookmark.class))).thenReturn(savedBookmark);

        // When
        BookmarkResponse response = contentSocialService.toggleBookmark(1L, bookmarkRequest, "testuser");

        // Then
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals(1L, response.getContentId());
        assertEquals("Favorites", response.getBookmarkCollection());
        assertEquals("Must try this recipe", response.getNotes());
        assertNotNull(response.getContent());
        assertEquals("Test Recipe", response.getContent().getTitle());

        verify(contentBookmarkRepository).save(any(ContentBookmark.class));
        verify(contentBookmarkRepository, never()).deleteByUserAndContent(any(), any());
    }

    @Test
    void toggleBookmark_RemoveBookmark_Success() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.existsByUserAndContent(testUser, testContent)).thenReturn(true);

        // When
        BookmarkResponse response = contentSocialService.toggleBookmark(1L, bookmarkRequest, "testuser");

        // Then
        assertNull(response); // Should return null when removing bookmark

        verify(contentBookmarkRepository).deleteByUserAndContent(testUser, testContent);
        verify(contentBookmarkRepository, never()).save(any(ContentBookmark.class));
    }

    @Test
    void isContentBookmarked_True() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.existsByUserAndContent(testUser, testContent)).thenReturn(true);

        // When
        boolean result = contentSocialService.isContentBookmarked(1L, "testuser");

        // Then
        assertTrue(result);
    }

    @Test
    void isContentBookmarked_False() {
        // Given
        when(contentRepository.findById(1L)).thenReturn(Optional.of(testContent));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.existsByUserAndContent(testUser, testContent)).thenReturn(false);

        // When
        boolean result = contentSocialService.isContentBookmarked(1L, "testuser");

        // Then
        assertFalse(result);
    }

    @Test
    void getUserBookmarks_Success() {
        // Given
        ContentBookmark bookmark = ContentBookmark.builder()
                .id(1L)
                .content(testContent)
                .user(testUser)
                .bookmarkCollection("Favorites")
                .notes("Great recipe")
                .createdAt(LocalDateTime.now())
                .build();

        List<ContentBookmark> bookmarks = Arrays.asList(bookmark);
        Page<ContentBookmark> bookmarkPage = new PageImpl<>(bookmarks);
        Pageable pageable = PageRequest.of(0, 10);

        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.findByUser(testUser, pageable)).thenReturn(bookmarkPage);

        // When
        Page<BookmarkResponse> response = contentSocialService.getUserBookmarks("testuser", null, pageable);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals(1L, response.getContent().get(0).getId());
        assertEquals("Favorites", response.getContent().get(0).getBookmarkCollection());
    }

    @Test
    void getUserBookmarkCollections_Success() {
        // Given
        List<String> collections = Arrays.asList("Favorites", "Watch Later", "Recipes");
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(contentBookmarkRepository.findBookmarkCollectionsByUser(testUser)).thenReturn(collections);

        // When
        List<String> result = contentSocialService.getUserBookmarkCollections("testuser");

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("Favorites"));
        assertTrue(result.contains("Watch Later"));
        assertTrue(result.contains("Recipes"));
    }
}
