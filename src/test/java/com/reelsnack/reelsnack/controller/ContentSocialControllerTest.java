package com.reelsnack.reelsnack.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.reelsnack.reelsnack.dto.request.BookmarkContentRequest;
import com.reelsnack.reelsnack.dto.request.ShareContentRequest;
import com.reelsnack.reelsnack.dto.response.BookmarkResponse;
import com.reelsnack.reelsnack.dto.response.ShareResponse;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.security.JwtUtils;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.ContentService;
import com.reelsnack.reelsnack.service.ContentSocialService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ContentController.class)
class ContentSocialControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ContentService contentService;

    @MockBean
    private ContentSocialService contentSocialService;

    @MockBean
    private JwtUtils jwtUtils;

    private UserDetailsImpl userDetails;
    private ShareContentRequest shareRequest;
    private BookmarkContentRequest bookmarkRequest;
    private ShareResponse shareResponse;
    private BookmarkResponse bookmarkResponse;

    @BeforeEach
    void setUp() {
        User testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();
        userDetails = UserDetailsImpl.build(testUser);

        shareRequest = ShareContentRequest.builder()
                .sharePlatform("FACEBOOK")
                .shareMessage("Check out this amazing recipe!")
                .build();

        bookmarkRequest = BookmarkContentRequest.builder()
                .bookmarkCollection("Favorites")
                .notes("Must try this recipe")
                .build();

        shareResponse = ShareResponse.builder()
                .id(1L)
                .contentId(1L)
                .contentTitle("Test Recipe")
                .sharePlatform("FACEBOOK")
                .shareMessage("Check out this amazing recipe!")
                .shareUrl("/api/v1/content/1/share")
                .sharedAt(LocalDateTime.now())
                .sharedBy(ShareResponse.UserSummaryResponse.builder()
                        .id(1L)
                        .username("testuser")
                        .firstName("Test")
                        .lastName("User")
                        .build())
                .build();

        bookmarkResponse = BookmarkResponse.builder()
                .id(1L)
                .contentId(1L)
                .bookmarkCollection("Favorites")
                .notes("Must try this recipe")
                .bookmarkedAt(LocalDateTime.now())
                .content(BookmarkResponse.ContentSummaryResponse.builder()
                        .id(1L)
                        .title("Test Recipe")
                        .description("A delicious test recipe")
                        .contentType("RECIPE_VIDEO")
                        .viewCount(100L)
                        .likeCount(10L)
                        .build())
                .build();
    }

    @Test
    @WithMockUser(username = "testuser")
    void shareContent_Success() throws Exception {
        // Given
        when(contentSocialService.shareContent(eq(1L), any(ShareContentRequest.class), eq("testuser")))
                .thenReturn(shareResponse);

        // When & Then
        mockMvc.perform(post("/api/v1/content/1/share")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(shareRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.contentId").value(1))
                .andExpect(jsonPath("$.contentTitle").value("Test Recipe"))
                .andExpect(jsonPath("$.sharePlatform").value("FACEBOOK"))
                .andExpect(jsonPath("$.shareMessage").value("Check out this amazing recipe!"))
                .andExpect(jsonPath("$.sharedBy.username").value("testuser"));
    }

    @Test
    void shareContent_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/content/1/share")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(shareRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void getContentShares_Success() throws Exception {
        // Given
        Page<ShareResponse> sharesPage = new PageImpl<>(Arrays.asList(shareResponse));
        when(contentSocialService.getContentShares(eq(1L), any(PageRequest.class)))
                .thenReturn(sharesPage);

        // When & Then
        mockMvc.perform(get("/api/v1/content/1/shares")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].contentTitle").value("Test Recipe"))
                .andExpect(jsonPath("$.totalElements").value(1));
    }

    @Test
    @WithMockUser(username = "testuser")
    void toggleBookmark_AddBookmark_Success() throws Exception {
        // Given
        when(contentSocialService.toggleBookmark(eq(1L), any(BookmarkContentRequest.class), eq("testuser")))
                .thenReturn(bookmarkResponse);

        // When & Then
        mockMvc.perform(post("/api/v1/content/1/bookmark")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(bookmarkRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.contentId").value(1))
                .andExpect(jsonPath("$.bookmarkCollection").value("Favorites"))
                .andExpect(jsonPath("$.notes").value("Must try this recipe"))
                .andExpect(jsonPath("$.content.title").value("Test Recipe"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void toggleBookmark_RemoveBookmark_Success() throws Exception {
        // Given - returning null indicates bookmark was removed
        when(contentSocialService.toggleBookmark(eq(1L), any(BookmarkContentRequest.class), eq("testuser")))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(post("/api/v1/content/1/bookmark")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(bookmarkRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(""));
    }

    @Test
    @WithMockUser(username = "testuser")
    void isContentBookmarked_True() throws Exception {
        // Given
        when(contentSocialService.isContentBookmarked(1L, "testuser")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/content/1/is-bookmarked")
                        .with(user(userDetails)))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void isContentBookmarked_False() throws Exception {
        // Given
        when(contentSocialService.isContentBookmarked(1L, "testuser")).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/api/v1/content/1/is-bookmarked")
                        .with(user(userDetails)))
                .andExpect(status().isOk())
                .andExpect(content().string("false"));
    }

    @Test
    void isContentBookmarked_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/content/1/is-bookmarked"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser")
    void shareContent_InvalidContentId() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/content/0/share")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(shareRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "testuser")
    void shareContent_InvalidRequest() throws Exception {
        // Given - request with invalid data
        ShareContentRequest invalidRequest = ShareContentRequest.builder()
                .sharePlatform("A".repeat(51)) // Exceeds max length
                .shareMessage("B".repeat(501)) // Exceeds max length
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/content/1/share")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "testuser")
    void toggleBookmark_InvalidRequest() throws Exception {
        // Given - request with invalid data
        BookmarkContentRequest invalidRequest = BookmarkContentRequest.builder()
                .bookmarkCollection("A".repeat(101)) // Exceeds max length
                .notes("B".repeat(501)) // Exceeds max length
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/content/1/bookmark")
                        .with(user(userDetails))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }
}
