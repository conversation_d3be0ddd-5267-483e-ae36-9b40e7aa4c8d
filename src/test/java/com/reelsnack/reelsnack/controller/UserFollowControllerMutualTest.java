package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.response.UserFollowResponse;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.security.JwtUtils;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.UserFollowService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(UserFollowController.class)
class UserFollowControllerMutualTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserFollowService userFollowService;

    @MockBean
    private JwtUtils jwtUtils;

    private UserDetailsImpl userDetails;
    private UserFollowResponse.UserSummaryResponse mutualFollower1;
    private UserFollowResponse.UserSummaryResponse mutualFollower2;

    @BeforeEach
    void setUp() {
        User testUser = User.builder()
                .id(1L)
                .username("current_user")
                .email("<EMAIL>")
                .firstName("Current")
                .lastName("User")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();
        userDetails = UserDetailsImpl.build(testUser);

        mutualFollower1 = UserFollowResponse.UserSummaryResponse.builder()
                .id(4L)
                .username("mutual_follower1")
                .firstName("Mutual")
                .lastName("Follower1")
                .profilePictureUrl("https://example.com/profile1.jpg")
                .isFollowedByCurrentUser(true)
                .followerCount(150L)
                .followingCount(75L)
                .contentCount(25L)
                .build();

        mutualFollower2 = UserFollowResponse.UserSummaryResponse.builder()
                .id(5L)
                .username("mutual_follower2")
                .firstName("Mutual")
                .lastName("Follower2")
                .profilePictureUrl("https://example.com/profile2.jpg")
                .isFollowedByCurrentUser(false)
                .followerCount(200L)
                .followingCount(100L)
                .contentCount(30L)
                .build();
    }

    @Test
    @WithMockUser(username = "current_user")
    void getMutualFollowers_WithAuthenticatedUser_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> mutualFollowersPage = 
                new PageImpl<>(Arrays.asList(mutualFollower1, mutualFollower2));
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                eq("current_user")))
                .thenReturn(mutualFollowersPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john")
                        .with(user(userDetails))
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(4))
                .andExpect(jsonPath("$.content[0].username").value("mutual_follower1"))
                .andExpect(jsonPath("$.content[0].firstName").value("Mutual"))
                .andExpect(jsonPath("$.content[0].lastName").value("Follower1"))
                .andExpect(jsonPath("$.content[0].isFollowedByCurrentUser").value(true))
                .andExpect(jsonPath("$.content[0].followerCount").value(150))
                .andExpect(jsonPath("$.content[1].id").value(5))
                .andExpect(jsonPath("$.content[1].username").value("mutual_follower2"))
                .andExpect(jsonPath("$.content[1].isFollowedByCurrentUser").value(false))
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    void getMutualFollowers_WithoutAuthentication_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> mutualFollowersPage = 
                new PageImpl<>(Arrays.asList(mutualFollower1, mutualFollower2));
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(mutualFollowersPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].username").value("mutual_follower1"))
                .andExpect(jsonPath("$.content[1].username").value("mutual_follower2"))
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    void getMutualFollowers_EmptyResult_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> emptyPage = new PageImpl<>(Arrays.asList());
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(emptyPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content").isEmpty())
                .andExpect(jsonPath("$.totalElements").value(0));
    }

    @Test
    void getMutualFollowers_CustomPagination_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> mutualFollowersPage = 
                new PageImpl<>(Arrays.asList(mutualFollower1));
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(mutualFollowersPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john")
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].username").value("mutual_follower1"));
    }

    @Test
    void getMutualFollowers_SameUser_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> emptyPage = new PageImpl<>(Arrays.asList());
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("chef_maria"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(emptyPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/chef_maria"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content").isEmpty())
                .andExpect(jsonPath("$.totalElements").value(0));
    }

    @Test
    void getMutualFollowers_InvalidUsernames_Success() throws Exception {
        // Given - Service should handle invalid usernames and throw appropriate exceptions
        // The controller should let the service handle validation
        Page<UserFollowResponse.UserSummaryResponse> emptyPage = new PageImpl<>(Arrays.asList());
        
        when(userFollowService.getMutualFollowers(
                eq("nonexistent_user1"), 
                eq("nonexistent_user2"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(emptyPage);

        // When & Then
        mockMvc.perform(get("/api/v1/follows/nonexistent_user1/mutual-followers/nonexistent_user2"))
                .andExpect(status().isOk());
    }

    @Test
    void getMutualFollowers_DefaultPagination_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> mutualFollowersPage = 
                new PageImpl<>(Arrays.asList(mutualFollower1, mutualFollower2));
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                isNull()))
                .thenReturn(mutualFollowersPage);

        // When & Then - Test default pagination values
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    @WithMockUser(username = "current_user")
    void getMutualFollowers_LargePage_Success() throws Exception {
        // Given
        Page<UserFollowResponse.UserSummaryResponse> mutualFollowersPage = 
                new PageImpl<>(Arrays.asList(mutualFollower1, mutualFollower2));
        
        when(userFollowService.getMutualFollowers(
                eq("chef_maria"), 
                eq("foodie_john"), 
                any(PageRequest.class), 
                eq("current_user")))
                .thenReturn(mutualFollowersPage);

        // When & Then - Test with larger page size
        mockMvc.perform(get("/api/v1/follows/chef_maria/mutual-followers/foodie_john")
                        .with(user(userDetails))
                        .param("page", "0")
                        .param("size", "100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.totalElements").value(2));
    }
}
