package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.response.BookmarkResponse;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.security.JwtUtils;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.ContentSocialService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(BookmarkController.class)
class BookmarkControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ContentSocialService contentSocialService;

    @MockBean
    private JwtUtils jwtUtils;

    private UserDetailsImpl userDetails;
    private BookmarkResponse bookmarkResponse1;
    private BookmarkResponse bookmarkResponse2;

    @BeforeEach
    void setUp() {
        User testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .password("password")
                .role(Role.ROLE_USER)
                .enabled(true)
                .emailVerified(true)
                .build();
        userDetails = UserDetailsImpl.build(testUser);

        bookmarkResponse1 = BookmarkResponse.builder()
                .id(1L)
                .contentId(1L)
                .bookmarkCollection("Favorites")
                .notes("Great recipe")
                .bookmarkedAt(LocalDateTime.now())
                .content(BookmarkResponse.ContentSummaryResponse.builder()
                        .id(1L)
                        .title("Pasta Recipe")
                        .description("Delicious pasta")
                        .contentType("RECIPE_VIDEO")
                        .viewCount(100L)
                        .likeCount(10L)
                        .creator(BookmarkResponse.UserSummaryResponse.builder()
                                .id(2L)
                                .username("chef_maria")
                                .firstName("Maria")
                                .lastName("Rodriguez")
                                .build())
                        .build())
                .build();

        bookmarkResponse2 = BookmarkResponse.builder()
                .id(2L)
                .contentId(2L)
                .bookmarkCollection("Watch Later")
                .notes("Watch this tutorial")
                .bookmarkedAt(LocalDateTime.now())
                .content(BookmarkResponse.ContentSummaryResponse.builder()
                        .id(2L)
                        .title("Cooking Tutorial")
                        .description("Learn basic cooking")
                        .contentType("TUTORIAL")
                        .viewCount(200L)
                        .likeCount(20L)
                        .creator(BookmarkResponse.UserSummaryResponse.builder()
                                .id(3L)
                                .username("chef_john")
                                .firstName("John")
                                .lastName("Smith")
                                .build())
                        .build())
                .build();
    }

    @Test
    @WithMockUser(username = "testuser")
    void getUserBookmarks_Success() throws Exception {
        // Given
        Page<BookmarkResponse> bookmarksPage = new PageImpl<>(Arrays.asList(bookmarkResponse1, bookmarkResponse2));
        when(contentSocialService.getUserBookmarks(eq("testuser"), isNull(), any(PageRequest.class)))
                .thenReturn(bookmarksPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks")
                        .with(user(userDetails))
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].bookmarkCollection").value("Favorites"))
                .andExpect(jsonPath("$.content[0].content.title").value("Pasta Recipe"))
                .andExpect(jsonPath("$.content[1].id").value(2))
                .andExpect(jsonPath("$.content[1].bookmarkCollection").value("Watch Later"))
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    @WithMockUser(username = "testuser")
    void getUserBookmarks_WithCollection_Success() throws Exception {
        // Given
        Page<BookmarkResponse> bookmarksPage = new PageImpl<>(Arrays.asList(bookmarkResponse1));
        when(contentSocialService.getUserBookmarks(eq("testuser"), eq("Favorites"), any(PageRequest.class)))
                .thenReturn(bookmarksPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks")
                        .with(user(userDetails))
                        .param("collection", "Favorites")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].bookmarkCollection").value("Favorites"))
                .andExpect(jsonPath("$.totalElements").value(1));
    }

    @Test
    void getUserBookmarks_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser")
    void getUserBookmarkCollections_Success() throws Exception {
        // Given
        List<String> collections = Arrays.asList("Favorites", "Watch Later", "Recipes");
        when(contentSocialService.getUserBookmarkCollections("testuser")).thenReturn(collections);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/collections")
                        .with(user(userDetails)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0]").value("Favorites"))
                .andExpect(jsonPath("$[1]").value("Watch Later"))
                .andExpect(jsonPath("$[2]").value("Recipes"))
                .andExpect(jsonPath("$.length()").value(3));
    }

    @Test
    void getUserBookmarkCollections_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/collections"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser")
    void searchUserBookmarks_Success() throws Exception {
        // Given
        Page<BookmarkResponse> searchResults = new PageImpl<>(Arrays.asList(bookmarkResponse1));
        when(contentSocialService.searchUserBookmarks(eq("testuser"), eq("pasta"), any(PageRequest.class)))
                .thenReturn(searchResults);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/search")
                        .with(user(userDetails))
                        .param("q", "pasta")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].content.title").value("Pasta Recipe"))
                .andExpect(jsonPath("$.totalElements").value(1));
    }

    @Test
    @WithMockUser(username = "testuser")
    void searchUserBookmarks_MissingQuery() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/search")
                        .with(user(userDetails))
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void searchUserBookmarks_Unauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/search")
                        .param("q", "pasta"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void getPublicUserBookmarks_Success() throws Exception {
        // Given
        Page<BookmarkResponse> bookmarksPage = new PageImpl<>(Arrays.asList(bookmarkResponse1));
        when(contentSocialService.getUserBookmarks(eq("chef_maria"), isNull(), any(PageRequest.class)))
                .thenReturn(bookmarksPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/user/chef_maria")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].bookmarkCollection").value("Favorites"))
                .andExpect(jsonPath("$.totalElements").value(1));
    }

    @Test
    void getPublicUserBookmarks_WithCollection_Success() throws Exception {
        // Given
        Page<BookmarkResponse> bookmarksPage = new PageImpl<>(Arrays.asList(bookmarkResponse1));
        when(contentSocialService.getUserBookmarks(eq("chef_maria"), eq("Favorites"), any(PageRequest.class)))
                .thenReturn(bookmarksPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks/user/chef_maria")
                        .param("collection", "Favorites")
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].bookmarkCollection").value("Favorites"))
                .andExpect(jsonPath("$.totalElements").value(1));
    }

    @Test
    @WithMockUser(username = "testuser")
    void getUserBookmarks_CustomPagination() throws Exception {
        // Given
        Page<BookmarkResponse> bookmarksPage = new PageImpl<>(Arrays.asList(bookmarkResponse1));
        when(contentSocialService.getUserBookmarks(eq("testuser"), isNull(), any(PageRequest.class)))
                .thenReturn(bookmarksPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks")
                        .with(user(userDetails))
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    @WithMockUser(username = "testuser")
    void getUserBookmarks_EmptyResult() throws Exception {
        // Given
        Page<BookmarkResponse> emptyPage = new PageImpl<>(Arrays.asList());
        when(contentSocialService.getUserBookmarks(eq("testuser"), isNull(), any(PageRequest.class)))
                .thenReturn(emptyPage);

        // When & Then
        mockMvc.perform(get("/api/v1/bookmarks")
                        .with(user(userDetails)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content").isEmpty())
                .andExpect(jsonPath("$.totalElements").value(0));
    }
}
