-- V12__create_content_social_features.sql
-- Create content sharing and bookmarking system

-- Create content_shares table
CREATE TABLE IF NOT EXISTS content_shares (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    share_platform VARCHAR(50),
    share_message VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (content_id) REFERENCES contents (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate shares (optional - can be removed to allow multiple shares)
    CONSTRAINT unique_user_content_share UNIQUE (user_id, content_id)
);

-- Create content_bookmarks table
CREATE TABLE IF NOT EXISTS content_bookmarks (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    bookmark_collection VARCHAR(100),
    notes <PERSON>RCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (content_id) REFERENCES contents (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate bookmarks
    CONSTRAINT unique_user_content_bookmark UNIQUE (user_id, content_id)
);

-- Create indexes for better performance

-- Content shares indexes
CREATE INDEX IF NOT EXISTS idx_content_shares_content_id ON content_shares (content_id);
CREATE INDEX IF NOT EXISTS idx_content_shares_user_id ON content_shares (user_id);
CREATE INDEX IF NOT EXISTS idx_content_shares_created_at ON content_shares (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_shares_content_created ON content_shares (content_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_shares_platform ON content_shares (share_platform);

-- Content bookmarks indexes
CREATE INDEX IF NOT EXISTS idx_content_bookmarks_content_id ON content_bookmarks (content_id);
CREATE INDEX IF NOT EXISTS idx_content_bookmarks_user_id ON content_bookmarks (user_id);
CREATE INDEX IF NOT EXISTS idx_content_bookmarks_created_at ON content_bookmarks (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_bookmarks_user_created ON content_bookmarks (user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_bookmarks_collection ON content_bookmarks (bookmark_collection);

-- Add comments for documentation
COMMENT ON TABLE content_shares IS 'User content sharing actions';
COMMENT ON COLUMN content_shares.content_id IS 'Reference to the shared content';
COMMENT ON COLUMN content_shares.user_id IS 'Reference to the user who shared the content';
COMMENT ON COLUMN content_shares.share_platform IS 'Platform where content was shared (FACEBOOK, TWITTER, etc.)';
COMMENT ON COLUMN content_shares.share_message IS 'Optional message included when sharing';
COMMENT ON COLUMN content_shares.created_at IS 'When the content was shared';

COMMENT ON TABLE content_bookmarks IS 'User content bookmarks/saves';
COMMENT ON COLUMN content_bookmarks.content_id IS 'Reference to the bookmarked content';
COMMENT ON COLUMN content_bookmarks.user_id IS 'Reference to the user who bookmarked the content';
COMMENT ON COLUMN content_bookmarks.bookmark_collection IS 'Optional collection name for organizing bookmarks';
COMMENT ON COLUMN content_bookmarks.notes IS 'Personal notes about the bookmarked content';
COMMENT ON COLUMN content_bookmarks.created_at IS 'When the content was bookmarked';

-- Create functions for maintaining share counts (optional)
CREATE OR REPLACE FUNCTION update_content_share_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE contents SET share_count = share_count + 1 WHERE id = NEW.content_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE contents SET share_count = GREATEST(share_count - 1, 0) WHERE id = OLD.content_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update share counts
DROP TRIGGER IF EXISTS trigger_update_share_count_insert ON content_shares;
CREATE TRIGGER trigger_update_share_count_insert
    AFTER INSERT ON content_shares
    FOR EACH ROW
    EXECUTE FUNCTION update_content_share_count();

DROP TRIGGER IF EXISTS trigger_update_share_count_delete ON content_shares;
CREATE TRIGGER trigger_update_share_count_delete
    AFTER DELETE ON content_shares
    FOR EACH ROW
    EXECUTE FUNCTION update_content_share_count();

-- Create materialized view for content engagement statistics (optional)
CREATE MATERIALIZED VIEW IF NOT EXISTS content_engagement_stats AS
SELECT 
    c.id as content_id,
    c.title,
    c.creator_id,
    c.view_count,
    c.like_count,
    c.comment_count,
    c.share_count,
    COALESCE(bookmark_counts.bookmark_count, 0) as bookmark_count,
    COALESCE(share_counts.actual_share_count, 0) as actual_share_count,
    (c.view_count + c.like_count * 2 + c.comment_count * 3 + c.share_count * 5 + COALESCE(bookmark_counts.bookmark_count, 0) * 2) as engagement_score
FROM contents c
LEFT JOIN (
    SELECT content_id, COUNT(*) as bookmark_count
    FROM content_bookmarks
    GROUP BY content_id
) bookmark_counts ON c.id = bookmark_counts.content_id
LEFT JOIN (
    SELECT content_id, COUNT(*) as actual_share_count
    FROM content_shares
    GROUP BY content_id
) share_counts ON c.id = share_counts.content_id
WHERE c.is_deleted = FALSE AND c.is_public = TRUE;

-- Create index on the materialized view
CREATE INDEX IF NOT EXISTS idx_content_engagement_stats_score ON content_engagement_stats (engagement_score DESC);
CREATE INDEX IF NOT EXISTS idx_content_engagement_stats_creator ON content_engagement_stats (creator_id);

-- Create function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_content_engagement_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW content_engagement_stats;
END;
$$ LANGUAGE plpgsql;

-- Note: You may want to set up a cron job or scheduled task to refresh this view periodically
-- For example: SELECT refresh_content_engagement_stats();
