-- V14: Add Restaurant-Chef Association
-- This migration creates the many-to-many relationship between restaurants and chefs

-- Create restaurant_chefs junction table
CREATE TABLE restaurant_chefs (
    restaurant_id BIGINT NOT NULL,
    chef_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (restaurant_id, chef_id),
    CONSTRAINT fk_restaurant_chefs_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    CONSTRAINT fk_restaurant_chefs_chef FOREIGN KEY (chef_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_restaurant_chefs_restaurant ON restaurant_chefs(restaurant_id);
CREATE INDEX idx_restaurant_chefs_chef ON restaurant_chefs(chef_id);

-- Add constraint to ensure only users with ROLE_CHEF can be associated
-- Note: This is a soft constraint - the application will enforce role checking
COMMENT ON TABLE restaurant_chefs IS 'Association table between restaurants and chefs (users with ROLE_CHEF)';
COMMENT ON COLUMN restaurant_chefs.chef_id IS 'Must reference a user with ROLE_CHEF';
