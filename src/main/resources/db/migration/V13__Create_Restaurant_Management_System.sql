-- V13: Create Restaurant Management System
-- This migration creates the complete restaurant management system including
-- restaurants, menus, categories, items, options, and operating hours

-- Create restaurants table
CREATE TABLE restaurants (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    owner_id BIGINT NOT NULL,
    address VARCHAR(200) NOT NULL,
    city VARCHAR(100),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    phone_number VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(255),
    logo_url VARCHAR(255),
    cover_image_url VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    is_open BOOLEAN NOT NULL DEFAULT FALSE,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    delivery_fee DECIMAL(8,2) DEFAULT 0.00,
    minimum_order DECIMAL(8,2) DEFAULT 0.00,
    estimated_delivery_time INTEGER DEFAULT 30,
    delivery_radius DECIMAL(5,2) DEFAULT 5.00,
    opening_time TIME,
    closing_time TIME,
    cuisine_types VARCHAR(500),
    special_features VARCHAR(500),
    admin_notes VARCHAR(1000),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_restaurant_owner FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_restaurant_rating CHECK (average_rating >= 0 AND average_rating <= 5),
    CONSTRAINT chk_restaurant_reviews CHECK (total_reviews >= 0),
    CONSTRAINT chk_restaurant_delivery_fee CHECK (delivery_fee >= 0),
    CONSTRAINT chk_restaurant_minimum_order CHECK (minimum_order >= 0),
    CONSTRAINT chk_restaurant_delivery_time CHECK (estimated_delivery_time > 0),
    CONSTRAINT chk_restaurant_delivery_radius CHECK (delivery_radius > 0),
    CONSTRAINT chk_restaurant_latitude CHECK (latitude >= -90 AND latitude <= 90),
    CONSTRAINT chk_restaurant_longitude CHECK (longitude >= -180 AND longitude <= 180)
);

-- Create menu categories table
CREATE TABLE menu_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    restaurant_id BIGINT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    image_url VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_category_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
);

-- Create menu items table
CREATE TABLE menu_items (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(1000),
    category_id BIGINT NOT NULL,
    price DECIMAL(8,2) NOT NULL,
    original_price DECIMAL(8,2),
    is_available BOOLEAN NOT NULL DEFAULT TRUE,
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    is_vegetarian BOOLEAN NOT NULL DEFAULT FALSE,
    is_vegan BOOLEAN NOT NULL DEFAULT FALSE,
    is_gluten_free BOOLEAN NOT NULL DEFAULT FALSE,
    is_spicy BOOLEAN NOT NULL DEFAULT FALSE,
    spice_level INTEGER DEFAULT 0,
    calories INTEGER,
    preparation_time INTEGER DEFAULT 15,
    sort_order INTEGER DEFAULT 0,
    image_url VARCHAR(255),
    ingredients VARCHAR(500),
    allergens VARCHAR(500),
    nutritional_info VARCHAR(500),
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_item_category FOREIGN KEY (category_id) REFERENCES menu_categories(id) ON DELETE CASCADE,
    CONSTRAINT chk_menu_item_price CHECK (price > 0),
    CONSTRAINT chk_menu_item_original_price CHECK (original_price IS NULL OR original_price >= 0),
    CONSTRAINT chk_menu_item_spice_level CHECK (spice_level >= 0 AND spice_level <= 5),
    CONSTRAINT chk_menu_item_calories CHECK (calories IS NULL OR calories >= 0),
    CONSTRAINT chk_menu_item_prep_time CHECK (preparation_time > 0),
    CONSTRAINT chk_menu_item_rating CHECK (average_rating >= 0 AND average_rating <= 5),
    CONSTRAINT chk_menu_item_reviews CHECK (total_reviews >= 0),
    CONSTRAINT chk_menu_item_orders CHECK (total_orders >= 0)
);

-- Create menu item options table
CREATE TABLE menu_item_options (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    menu_item_id BIGINT NOT NULL,
    option_type VARCHAR(50) NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    is_multiple_choice BOOLEAN NOT NULL DEFAULT FALSE,
    min_selections INTEGER DEFAULT 0,
    max_selections INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_item_option_item FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE CASCADE,
    CONSTRAINT chk_menu_item_option_min_selections CHECK (min_selections >= 0),
    CONSTRAINT chk_menu_item_option_max_selections CHECK (max_selections >= 1),
    CONSTRAINT chk_menu_item_option_selections CHECK (max_selections >= min_selections)
);

-- Create menu item option values table
CREATE TABLE menu_item_option_values (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    option_id BIGINT NOT NULL,
    additional_price DECIMAL(8,2) NOT NULL DEFAULT 0.00,
    is_available BOOLEAN NOT NULL DEFAULT TRUE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    additional_calories INTEGER DEFAULT 0,
    additional_prep_time INTEGER DEFAULT 0,
    image_url VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_item_option_value_option FOREIGN KEY (option_id) REFERENCES menu_item_options(id) ON DELETE CASCADE,
    CONSTRAINT chk_menu_item_option_value_price CHECK (additional_price >= 0),
    CONSTRAINT chk_menu_item_option_value_calories CHECK (additional_calories >= 0),
    CONSTRAINT chk_menu_item_option_value_prep_time CHECK (additional_prep_time >= 0)
);

-- Create restaurant hours table
CREATE TABLE restaurant_hours (
    id BIGSERIAL PRIMARY KEY,
    restaurant_id BIGINT NOT NULL,
    day_of_week VARCHAR(10) NOT NULL,
    is_open BOOLEAN NOT NULL DEFAULT TRUE,
    opening_time TIME,
    closing_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    is_24_hours BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_restaurant_hours_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    CONSTRAINT unique_restaurant_day UNIQUE (restaurant_id, day_of_week)
);

-- Create indexes for better performance
CREATE INDEX idx_restaurant_owner ON restaurants(owner_id);
CREATE INDEX idx_restaurant_status ON restaurants(status);
CREATE INDEX idx_restaurant_location ON restaurants(latitude, longitude);
CREATE INDEX idx_restaurant_rating ON restaurants(average_rating DESC);
CREATE INDEX idx_restaurant_created ON restaurants(created_at DESC);

CREATE INDEX idx_menu_category_restaurant ON menu_categories(restaurant_id);
CREATE INDEX idx_menu_category_active ON menu_categories(is_active);
CREATE INDEX idx_menu_category_sort ON menu_categories(restaurant_id, sort_order);

CREATE INDEX idx_menu_item_category ON menu_items(category_id);
CREATE INDEX idx_menu_item_available ON menu_items(is_available);
CREATE INDEX idx_menu_item_featured ON menu_items(is_featured);
CREATE INDEX idx_menu_item_price ON menu_items(price);
CREATE INDEX idx_menu_item_sort ON menu_items(category_id, sort_order);

CREATE INDEX idx_menu_item_option_item ON menu_item_options(menu_item_id);
CREATE INDEX idx_menu_item_option_type ON menu_item_options(option_type);
CREATE INDEX idx_menu_item_option_sort ON menu_item_options(menu_item_id, sort_order);

CREATE INDEX idx_menu_item_option_value_option ON menu_item_option_values(option_id);
CREATE INDEX idx_menu_item_option_value_available ON menu_item_option_values(is_available);
CREATE INDEX idx_menu_item_option_value_sort ON menu_item_option_values(option_id, sort_order);

CREATE INDEX idx_restaurant_hours_restaurant ON restaurant_hours(restaurant_id);
CREATE INDEX idx_restaurant_hours_day ON restaurant_hours(day_of_week);
CREATE INDEX idx_restaurant_hours_open ON restaurant_hours(is_open);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_restaurants_updated_at BEFORE UPDATE ON restaurants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_categories_updated_at BEFORE UPDATE ON menu_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_item_options_updated_at BEFORE UPDATE ON menu_item_options
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_item_option_values_updated_at BEFORE UPDATE ON menu_item_option_values
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_restaurant_hours_updated_at BEFORE UPDATE ON restaurant_hours
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
