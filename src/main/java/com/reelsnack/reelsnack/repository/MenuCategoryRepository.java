package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.MenuCategory;
import com.reelsnack.reelsnack.model.Restaurant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MenuCategory entity operations.
 */
@Repository
public interface MenuCategoryRepository extends JpaRepository<MenuCategory, Long> {

    /**
     * Find categories by restaurant
     */
    List<MenuCategory> findByRestaurantOrderBySortOrderAscNameAsc(Restaurant restaurant);

    /**
     * Find categories by restaurant ID
     */
    List<MenuCategory> findByRestaurantIdOrderBySortOrderAscNameAsc(Long restaurantId);

    /**
     * Find active categories by restaurant
     */
    List<MenuCategory> findByRestaurantAndIsActiveTrueOrderBySortOrderAscNameAsc(Restaurant restaurant);

    /**
     * Find active categories by restaurant ID
     */
    List<MenuCategory> findByRestaurantIdAndIsActiveTrueOrderBySortOrderAscNameAsc(Long restaurantId);

    /**
     * Find categories by restaurant with pagination
     */
    Page<MenuCategory> findByRestaurant(Restaurant restaurant, Pageable pageable);

    /**
     * Find categories by restaurant ID with pagination
     */
    Page<MenuCategory> findByRestaurantId(Long restaurantId, Pageable pageable);

    /**
     * Find active categories by restaurant with pagination
     */
    Page<MenuCategory> findByRestaurantAndIsActiveTrue(Restaurant restaurant, Pageable pageable);

    /**
     * Search categories by name within restaurant
     */
    @Query("SELECT mc FROM MenuCategory mc WHERE mc.restaurant = :restaurant AND " +
           "LOWER(mc.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<MenuCategory> findByRestaurantAndNameContainingIgnoreCase(@Param("restaurant") Restaurant restaurant,
                                                                  @Param("name") String name);

    /**
     * Find category by restaurant and name
     */
    Optional<MenuCategory> findByRestaurantAndNameIgnoreCase(Restaurant restaurant, String name);

    /**
     * Find category by restaurant ID and name
     */
    Optional<MenuCategory> findByRestaurantIdAndNameIgnoreCase(Long restaurantId, String name);

    /**
     * Check if category name exists in restaurant (excluding specific category)
     */
    boolean existsByRestaurantAndNameIgnoreCaseAndIdNot(Restaurant restaurant, String name, Long id);

    /**
     * Check if category name exists in restaurant
     */
    boolean existsByRestaurantAndNameIgnoreCase(Restaurant restaurant, String name);

    /**
     * Count categories by restaurant
     */
    long countByRestaurant(Restaurant restaurant);

    /**
     * Count active categories by restaurant
     */
    long countByRestaurantAndIsActiveTrue(Restaurant restaurant);

    /**
     * Find categories with available menu items
     */
    @Query("SELECT DISTINCT mc FROM MenuCategory mc " +
           "JOIN mc.menuItems mi WHERE mc.restaurant = :restaurant AND mc.isActive = true AND mi.isAvailable = true")
    List<MenuCategory> findCategoriesWithAvailableItems(@Param("restaurant") Restaurant restaurant);

    /**
     * Find categories with available menu items by restaurant ID
     */
    @Query("SELECT DISTINCT mc FROM MenuCategory mc " +
           "JOIN mc.menuItems mi WHERE mc.restaurant.id = :restaurantId AND mc.isActive = true AND mi.isAvailable = true")
    List<MenuCategory> findCategoriesWithAvailableItemsByRestaurantId(@Param("restaurantId") Long restaurantId);

    /**
     * Get next sort order for restaurant
     */
    @Query("SELECT COALESCE(MAX(mc.sortOrder), 0) + 1 FROM MenuCategory mc WHERE mc.restaurant = :restaurant")
    Integer getNextSortOrder(@Param("restaurant") Restaurant restaurant);

    /**
     * Find categories by sort order range
     */
    @Query("SELECT mc FROM MenuCategory mc WHERE mc.restaurant = :restaurant AND " +
           "mc.sortOrder BETWEEN :startOrder AND :endOrder ORDER BY mc.sortOrder")
    List<MenuCategory> findByRestaurantAndSortOrderBetween(@Param("restaurant") Restaurant restaurant,
                                                          @Param("startOrder") Integer startOrder,
                                                          @Param("endOrder") Integer endOrder);

    /**
     * Update sort orders for categories after a specific order
     */
    @Query("UPDATE MenuCategory mc SET mc.sortOrder = mc.sortOrder + :increment " +
           "WHERE mc.restaurant = :restaurant AND mc.sortOrder >= :fromOrder")
    void updateSortOrdersAfter(@Param("restaurant") Restaurant restaurant,
                              @Param("fromOrder") Integer fromOrder,
                              @Param("increment") Integer increment);

    /**
     * Get category statistics for restaurant
     */
    @Query("SELECT " +
           "COUNT(mc) as totalCategories, " +
           "COUNT(CASE WHEN mc.isActive = true THEN 1 END) as activeCategories, " +
           "COUNT(DISTINCT mi.id) as totalMenuItems, " +
           "COUNT(CASE WHEN mi.isAvailable = true THEN 1 END) as availableMenuItems " +
           "FROM MenuCategory mc LEFT JOIN mc.menuItems mi WHERE mc.restaurant = :restaurant")
    Object[] getCategoryStatistics(@Param("restaurant") Restaurant restaurant);
}
