package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.ContentShare;
import com.reelsnack.reelsnack.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Repository interface for ContentShare entity operations.
 */
@Repository
public interface ContentShareRepository extends JpaRepository<ContentShare, Long> {

    // ===== Basic Share Operations =====

    /**
     * Check if a user has shared specific content.
     * @param user The user
     * @param content The content
     * @return true if the user has shared the content
     */
    boolean existsByUserAndContent(User user, Content content);

    /**
     * Find a specific share by user and content.
     * @param user The user
     * @param content The content
     * @return Optional ContentShare if exists
     */
    Optional<ContentShare> findByUserAndContent(User user, Content content);

    /**
     * Delete a share by user and content.
     * @param user The user
     * @param content The content
     */
    void deleteByUserAndContent(User user, Content content);

    // ===== Count Queries =====

    /**
     * Count total shares for a content.
     * @param content The content
     * @return Number of shares
     */
    long countByContent(Content content);

    /**
     * Count shares by a user.
     * @param user The user
     * @return Number of shares by the user
     */
    long countByUser(User user);

    // ===== Query Operations =====

    /**
     * Get all shares for a content.
     * @param content The content
     * @param pageable Pagination information
     * @return Page of shares
     */
    @Query("SELECT cs FROM ContentShare cs " +
           "LEFT JOIN FETCH cs.user " +
           "WHERE cs.content = :content " +
           "ORDER BY cs.createdAt DESC")
    Page<ContentShare> findByContent(@Param("content") Content content, Pageable pageable);

    /**
     * Get all shares by a user.
     * @param user The user
     * @param pageable Pagination information
     * @return Page of shares
     */
    @Query("SELECT cs FROM ContentShare cs " +
           "LEFT JOIN FETCH cs.content " +
           "WHERE cs.user = :user " +
           "ORDER BY cs.createdAt DESC")
    Page<ContentShare> findByUser(@Param("user") User user, Pageable pageable);

    /**
     * Get shares by platform.
     * @param platform The share platform
     * @param pageable Pagination information
     * @return Page of shares
     */
    Page<ContentShare> findBySharePlatform(String platform, Pageable pageable);

    /**
     * Get recent shares for analytics.
     * @param since Date to get shares since
     * @param pageable Pagination information
     * @return Page of recent shares
     */
    @Query("SELECT cs FROM ContentShare cs " +
           "WHERE cs.createdAt >= :since " +
           "ORDER BY cs.createdAt DESC")
    Page<ContentShare> findRecentShares(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * Get most shared content.
     * @param pageable Pagination information
     * @return Page of content with share counts
     */
    @Query("SELECT cs.content, COUNT(cs) as shareCount " +
           "FROM ContentShare cs " +
           "GROUP BY cs.content " +
           "ORDER BY COUNT(cs) DESC")
    Page<Object[]> findMostSharedContent(Pageable pageable);

    /**
     * Get share statistics by platform.
     * @return List of platform share counts
     */
    @Query("SELECT cs.sharePlatform, COUNT(cs) as shareCount " +
           "FROM ContentShare cs " +
           "WHERE cs.sharePlatform IS NOT NULL " +
           "GROUP BY cs.sharePlatform " +
           "ORDER BY COUNT(cs) DESC")
    Page<Object[]> getShareStatsByPlatform(Pageable pageable);
}
