package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.ContentBookmark;
import com.reelsnack.reelsnack.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for ContentBookmark entity operations.
 */
@Repository
public interface ContentBookmarkRepository extends JpaRepository<ContentBookmark, Long> {

    // ===== Basic Bookmark Operations =====

    /**
     * Check if a user has bookmarked specific content.
     * @param user The user
     * @param content The content
     * @return true if the user has bookmarked the content
     */
    boolean existsByUserAndContent(User user, Content content);

    /**
     * Find a specific bookmark by user and content.
     * @param user The user
     * @param content The content
     * @return Optional ContentBookmark if exists
     */
    Optional<ContentBookmark> findByUserAndContent(User user, Content content);

    /**
     * Delete a bookmark by user and content.
     * @param user The user
     * @param content The content
     */
    void deleteByUserAndContent(User user, Content content);

    // ===== Count Queries =====

    /**
     * Count bookmarks for a content.
     * @param content The content
     * @return Number of bookmarks
     */
    long countByContent(Content content);

    /**
     * Count bookmarks by a user.
     * @param user The user
     * @return Number of bookmarks by the user
     */
    long countByUser(User user);

    // ===== Query Operations =====

    /**
     * Get all bookmarks by a user.
     * @param user The user
     * @param pageable Pagination information
     * @return Page of bookmarks
     */
    @Query("SELECT cb FROM ContentBookmark cb " +
           "LEFT JOIN FETCH cb.content " +
           "WHERE cb.user = :user " +
           "ORDER BY cb.createdAt DESC")
    Page<ContentBookmark> findByUser(@Param("user") User user, Pageable pageable);

    /**
     * Get bookmarks by user and collection.
     * @param user The user
     * @param collection The collection name
     * @param pageable Pagination information
     * @return Page of bookmarks
     */
    @Query("SELECT cb FROM ContentBookmark cb " +
           "LEFT JOIN FETCH cb.content " +
           "WHERE cb.user = :user AND cb.bookmarkCollection = :collection " +
           "ORDER BY cb.createdAt DESC")
    Page<ContentBookmark> findByUserAndBookmarkCollection(@Param("user") User user, 
                                                          @Param("collection") String collection, 
                                                          Pageable pageable);

    /**
     * Get all bookmark collections for a user.
     * @param user The user
     * @return List of collection names
     */
    @Query("SELECT DISTINCT cb.bookmarkCollection FROM ContentBookmark cb " +
           "WHERE cb.user = :user AND cb.bookmarkCollection IS NOT NULL " +
           "ORDER BY cb.bookmarkCollection")
    List<String> findBookmarkCollectionsByUser(@Param("user") User user);

    /**
     * Get most bookmarked content.
     * @param pageable Pagination information
     * @return Page of content with bookmark counts
     */
    @Query("SELECT cb.content, COUNT(cb) as bookmarkCount " +
           "FROM ContentBookmark cb " +
           "GROUP BY cb.content " +
           "ORDER BY COUNT(cb) DESC")
    Page<Object[]> findMostBookmarkedContent(Pageable pageable);

    /**
     * Get bookmarks for a content.
     * @param content The content
     * @param pageable Pagination information
     * @return Page of bookmarks
     */
    @Query("SELECT cb FROM ContentBookmark cb " +
           "LEFT JOIN FETCH cb.user " +
           "WHERE cb.content = :content " +
           "ORDER BY cb.createdAt DESC")
    Page<ContentBookmark> findByContent(@Param("content") Content content, Pageable pageable);

    /**
     * Search bookmarks by content title or notes.
     * @param user The user
     * @param searchTerm The search term
     * @param pageable Pagination information
     * @return Page of matching bookmarks
     */
    @Query("SELECT cb FROM ContentBookmark cb " +
           "LEFT JOIN FETCH cb.content c " +
           "WHERE cb.user = :user AND " +
           "(LOWER(c.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           " LOWER(cb.notes) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "ORDER BY cb.createdAt DESC")
    Page<ContentBookmark> searchBookmarks(@Param("user") User user, 
                                         @Param("searchTerm") String searchTerm, 
                                         Pageable pageable);
}
