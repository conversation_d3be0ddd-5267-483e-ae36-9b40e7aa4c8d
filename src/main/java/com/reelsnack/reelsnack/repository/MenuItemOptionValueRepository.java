package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.MenuItemOption;
import com.reelsnack.reelsnack.model.MenuItemOptionValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MenuItemOptionValue entity operations.
 */
@Repository
public interface MenuItemOptionValueRepository extends JpaRepository<MenuItemOptionValue, Long> {

    /**
     * Find values by option
     */
    List<MenuItemOptionValue> findByOptionOrderBySortOrderAscNameAsc(MenuItemOption option);

    /**
     * Find values by option ID
     */
    List<MenuItemOptionValue> findByOptionIdOrderBySortOrderAscNameAsc(Long optionId);

    /**
     * Find available values by option
     */
    List<MenuItemOptionValue> findByOptionAndIsAvailableTrueOrderBySortOrderAscNameAsc(MenuItemOption option);

    /**
     * Find available values by option ID
     */
    List<MenuItemOptionValue> findByOptionIdAndIsAvailableTrueOrderBySortOrderAscNameAsc(Long optionId);

    /**
     * Find default value by option
     */
    Optional<MenuItemOptionValue> findByOptionAndIsDefaultTrue(MenuItemOption option);

    /**
     * Find value by option and name
     */
    Optional<MenuItemOptionValue> findByOptionAndNameIgnoreCase(MenuItemOption option, String name);

    /**
     * Check if value name exists in option (excluding specific value)
     */
    boolean existsByOptionAndNameIgnoreCaseAndIdNot(MenuItemOption option, String name, Long id);

    /**
     * Check if value name exists in option
     */
    boolean existsByOptionAndNameIgnoreCase(MenuItemOption option, String name);

    /**
     * Count values by option
     */
    long countByOption(MenuItemOption option);

    /**
     * Count available values by option
     */
    long countByOptionAndIsAvailableTrue(MenuItemOption option);

    /**
     * Get next sort order for option
     */
    @Query("SELECT COALESCE(MAX(miov.sortOrder), 0) + 1 FROM MenuItemOptionValue miov WHERE miov.option = :option")
    Integer getNextSortOrder(@Param("option") MenuItemOption option);

    /**
     * Find free values (no additional cost) by option
     */
    @Query("SELECT miov FROM MenuItemOptionValue miov WHERE miov.option = :option AND miov.additionalPrice = 0 AND miov.isAvailable = true")
    List<MenuItemOptionValue> findFreeValuesByOption(@Param("option") MenuItemOption option);

    /**
     * Find values with additional cost by option
     */
    @Query("SELECT miov FROM MenuItemOptionValue miov WHERE miov.option = :option AND miov.additionalPrice > 0 AND miov.isAvailable = true")
    List<MenuItemOptionValue> findPaidValuesByOption(@Param("option") MenuItemOption option);
}
