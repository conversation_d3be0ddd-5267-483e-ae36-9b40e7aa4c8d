package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.MenuCategory;
import com.reelsnack.reelsnack.model.MenuItem;
import com.reelsnack.reelsnack.model.Restaurant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MenuItem entity operations.
 */
@Repository
public interface MenuItemRepository extends JpaRepository<MenuItem, Long> {

    /**
     * Find items by category
     */
    List<MenuItem> findByCategoryOrderBySortOrderAscNameAsc(MenuCategory category);

    /**
     * Find items by category ID
     */
    List<MenuItem> findByCategoryIdOrderBySortOrderAscNameAsc(Long categoryId);

    /**
     * Find available items by category
     */
    List<MenuItem> findByCategoryAndIsAvailableTrueOrderBySortOrderAscNameAsc(MenuCategory category);

    /**
     * Find available items by category ID
     */
    List<MenuItem> findByCategoryIdAndIsAvailableTrueOrderBySortOrderAscNameAsc(Long categoryId);

    /**
     * Find items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant")
    Page<MenuItem> findByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find items by restaurant ID
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant.id = :restaurantId")
    Page<MenuItem> findByRestaurantId(@Param("restaurantId") Long restaurantId, Pageable pageable);

    /**
     * Find available items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isAvailable = true")
    Page<MenuItem> findAvailableByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find featured items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isFeatured = true AND mi.isAvailable = true")
    List<MenuItem> findFeaturedByRestaurant(@Param("restaurant") Restaurant restaurant);

    /**
     * Find featured items by restaurant ID
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant.id = :restaurantId AND mi.isFeatured = true AND mi.isAvailable = true")
    List<MenuItem> findFeaturedByRestaurantId(@Param("restaurantId") Long restaurantId);

    /**
     * Search items by name within restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND " +
           "LOWER(mi.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Page<MenuItem> findByRestaurantAndNameContainingIgnoreCase(@Param("restaurant") Restaurant restaurant,
                                                              @Param("name") String name,
                                                              Pageable pageable);

    /**
     * Search items by name within restaurant ID
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant.id = :restaurantId AND " +
           "LOWER(mi.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Page<MenuItem> findByRestaurantIdAndNameContainingIgnoreCase(@Param("restaurantId") Long restaurantId,
                                                                @Param("name") String name,
                                                                Pageable pageable);

    /**
     * Find items by price range within restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND " +
           "mi.price BETWEEN :minPrice AND :maxPrice")
    Page<MenuItem> findByRestaurantAndPriceBetween(@Param("restaurant") Restaurant restaurant,
                                                  @Param("minPrice") BigDecimal minPrice,
                                                  @Param("maxPrice") BigDecimal maxPrice,
                                                  Pageable pageable);

    /**
     * Find vegetarian items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isVegetarian = true AND mi.isAvailable = true")
    Page<MenuItem> findVegetarianByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find vegan items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isVegan = true AND mi.isAvailable = true")
    Page<MenuItem> findVeganByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find gluten-free items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isGlutenFree = true AND mi.isAvailable = true")
    Page<MenuItem> findGlutenFreeByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find items on sale by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND " +
           "mi.originalPrice IS NOT NULL AND mi.originalPrice > mi.price AND mi.isAvailable = true")
    Page<MenuItem> findOnSaleByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find top-rated items by restaurant
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND " +
           "mi.totalReviews >= :minReviews AND mi.isAvailable = true ORDER BY mi.averageRating DESC")
    Page<MenuItem> findTopRatedByRestaurant(@Param("restaurant") Restaurant restaurant,
                                           @Param("minReviews") Integer minReviews,
                                           Pageable pageable);

    /**
     * Find popular items by restaurant (most ordered)
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND " +
           "mi.isAvailable = true ORDER BY mi.totalOrders DESC")
    Page<MenuItem> findPopularByRestaurant(@Param("restaurant") Restaurant restaurant, Pageable pageable);

    /**
     * Find item by category and name
     */
    Optional<MenuItem> findByCategoryAndNameIgnoreCase(MenuCategory category, String name);

    /**
     * Check if item name exists in category (excluding specific item)
     */
    boolean existsByCategoryAndNameIgnoreCaseAndIdNot(MenuCategory category, String name, Long id);

    /**
     * Check if item name exists in category
     */
    boolean existsByCategoryAndNameIgnoreCase(MenuCategory category, String name);

    /**
     * Count items by category
     */
    long countByCategory(MenuCategory category);

    /**
     * Count available items by category
     */
    long countByCategoryAndIsAvailableTrue(MenuCategory category);

    /**
     * Count items by restaurant
     */
    @Query("SELECT COUNT(mi) FROM MenuItem mi WHERE mi.category.restaurant = :restaurant")
    long countByRestaurant(@Param("restaurant") Restaurant restaurant);

    /**
     * Get next sort order for category
     */
    @Query("SELECT COALESCE(MAX(mi.sortOrder), 0) + 1 FROM MenuItem mi WHERE mi.category = :category")
    Integer getNextSortOrder(@Param("category") MenuCategory category);

    /**
     * Find items by multiple dietary filters
     */
    @Query("SELECT mi FROM MenuItem mi WHERE mi.category.restaurant = :restaurant AND mi.isAvailable = true AND " +
           "(:isVegetarian IS NULL OR mi.isVegetarian = :isVegetarian) AND " +
           "(:isVegan IS NULL OR mi.isVegan = :isVegan) AND " +
           "(:isGlutenFree IS NULL OR mi.isGlutenFree = :isGlutenFree) AND " +
           "(:maxSpiceLevel IS NULL OR mi.spiceLevel <= :maxSpiceLevel)")
    Page<MenuItem> findByDietaryFilters(@Param("restaurant") Restaurant restaurant,
                                       @Param("isVegetarian") Boolean isVegetarian,
                                       @Param("isVegan") Boolean isVegan,
                                       @Param("isGlutenFree") Boolean isGlutenFree,
                                       @Param("maxSpiceLevel") Integer maxSpiceLevel,
                                       Pageable pageable);

    /**
     * Get menu item statistics for restaurant
     */
    @Query("SELECT " +
           "COUNT(mi) as totalItems, " +
           "COUNT(CASE WHEN mi.isAvailable = true THEN 1 END) as availableItems, " +
           "COUNT(CASE WHEN mi.isFeatured = true THEN 1 END) as featuredItems, " +
           "COUNT(CASE WHEN mi.isVegetarian = true THEN 1 END) as vegetarianItems, " +
           "AVG(mi.price) as averagePrice, " +
           "AVG(mi.averageRating) as averageRating " +
           "FROM MenuItem mi WHERE mi.category.restaurant = :restaurant")
    Object[] getMenuItemStatistics(@Param("restaurant") Restaurant restaurant);
}
