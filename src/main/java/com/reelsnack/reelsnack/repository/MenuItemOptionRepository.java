package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.MenuItem;
import com.reelsnack.reelsnack.model.MenuItemOption;
import com.reelsnack.reelsnack.model.enums.OptionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MenuItemOption entity operations.
 */
@Repository
public interface MenuItemOptionRepository extends JpaRepository<MenuItemOption, Long> {

    /**
     * Find options by menu item
     */
    List<MenuItemOption> findByMenuItemOrderBySortOrderAscNameAsc(MenuItem menuItem);

    /**
     * Find options by menu item ID
     */
    List<MenuItemOption> findByMenuItemIdOrderBySortOrderAscNameAsc(Long menuItemId);

    /**
     * Find options by menu item and option type
     */
    List<MenuItemOption> findByMenuItemAndOptionType(MenuItem menuItem, OptionType optionType);

    /**
     * Find required options by menu item
     */
    List<MenuItemOption> findByMenuItemAndIsRequiredTrueOrderBySortOrderAscNameAsc(MenuItem menuItem);

    /**
     * Find option by menu item and name
     */
    Optional<MenuItemOption> findByMenuItemAndNameIgnoreCase(MenuItem menuItem, String name);

    /**
     * Check if option name exists in menu item (excluding specific option)
     */
    boolean existsByMenuItemAndNameIgnoreCaseAndIdNot(MenuItem menuItem, String name, Long id);

    /**
     * Check if option name exists in menu item
     */
    boolean existsByMenuItemAndNameIgnoreCase(MenuItem menuItem, String name);

    /**
     * Count options by menu item
     */
    long countByMenuItem(MenuItem menuItem);

    /**
     * Count required options by menu item
     */
    long countByMenuItemAndIsRequiredTrue(MenuItem menuItem);

    /**
     * Find options with available values
     */
    @Query("SELECT DISTINCT mio FROM MenuItemOption mio " +
           "JOIN mio.values miov WHERE mio.menuItem = :menuItem AND miov.isAvailable = true")
    List<MenuItemOption> findOptionsWithAvailableValues(@Param("menuItem") MenuItem menuItem);

    /**
     * Get next sort order for menu item
     */
    @Query("SELECT COALESCE(MAX(mio.sortOrder), 0) + 1 FROM MenuItemOption mio WHERE mio.menuItem = :menuItem")
    Integer getNextSortOrder(@Param("menuItem") MenuItem menuItem);

    /**
     * Find options by type across restaurant
     */
    @Query("SELECT mio FROM MenuItemOption mio WHERE mio.menuItem.category.restaurant.id = :restaurantId AND mio.optionType = :optionType")
    List<MenuItemOption> findByRestaurantAndOptionType(@Param("restaurantId") Long restaurantId, @Param("optionType") OptionType optionType);
}
