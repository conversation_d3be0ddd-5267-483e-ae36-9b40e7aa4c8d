package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Restaurant;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Restaurant entity operations.
 */
@Repository
public interface RestaurantRepository extends JpaRepository<Restaurant, Long> {

    /**
     * Find restaurant by owner
     */
    Optional<Restaurant> findByOwner(User owner);

    /**
     * Find restaurant by owner ID
     */
    Optional<Restaurant> findByOwnerId(Long ownerId);

    /**
     * Find restaurants by status
     */
    Page<Restaurant> findByStatus(RestaurantStatus status, Pageable pageable);

    /**
     * Find active restaurants
     */
    Page<Restaurant> findByStatusAndIsOpenTrue(RestaurantStatus status, Pageable pageable);

    /**
     * Find featured restaurants
     */
    Page<Restaurant> findByIsFeaturedTrueAndStatus(RestaurantStatus status, Pageable pageable);

    /**
     * Find verified restaurants
     */
    Page<Restaurant> findByIsVerifiedTrueAndStatus(RestaurantStatus status, Pageable pageable);

    /**
     * Search restaurants by name (case-insensitive)
     */
    @Query("SELECT r FROM Restaurant r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%')) AND r.status = :status")
    Page<Restaurant> findByNameContainingIgnoreCaseAndStatus(@Param("name") String name, 
                                                           @Param("status") RestaurantStatus status, 
                                                           Pageable pageable);

    /**
     * Search restaurants by cuisine type
     */
    @Query("SELECT r FROM Restaurant r WHERE LOWER(r.cuisineTypes) LIKE LOWER(CONCAT('%', :cuisine, '%')) AND r.status = :status")
    Page<Restaurant> findByCuisineTypesContainingIgnoreCaseAndStatus(@Param("cuisine") String cuisine, 
                                                                   @Param("status") RestaurantStatus status, 
                                                                   Pageable pageable);

    /**
     * Find restaurants by city
     */
    Page<Restaurant> findByCityIgnoreCaseAndStatus(String city, RestaurantStatus status, Pageable pageable);

    /**
     * Find restaurants within delivery radius of given coordinates
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = :status AND r.isOpen = true AND " +
           "r.latitude IS NOT NULL AND r.longitude IS NOT NULL AND " +
           "(6371 * acos(cos(radians(:latitude)) * cos(radians(r.latitude)) * " +
           "cos(radians(r.longitude) - radians(:longitude)) + sin(radians(:latitude)) * " +
           "sin(radians(r.latitude)))) <= r.deliveryRadius")
    Page<Restaurant> findRestaurantsWithinDeliveryRadius(@Param("latitude") BigDecimal latitude,
                                                        @Param("longitude") BigDecimal longitude,
                                                        @Param("status") RestaurantStatus status,
                                                        Pageable pageable);

    /**
     * Find top-rated restaurants
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = :status AND r.totalReviews >= :minReviews " +
           "ORDER BY r.averageRating DESC, r.totalReviews DESC")
    Page<Restaurant> findTopRatedRestaurants(@Param("status") RestaurantStatus status,
                                           @Param("minReviews") Integer minReviews,
                                           Pageable pageable);

    /**
     * Find restaurants by minimum order amount range
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = :status AND r.minimumOrder BETWEEN :minAmount AND :maxAmount")
    Page<Restaurant> findByMinimumOrderBetween(@Param("minAmount") BigDecimal minAmount,
                                             @Param("maxAmount") BigDecimal maxAmount,
                                             @Param("status") RestaurantStatus status,
                                             Pageable pageable);

    /**
     * Find restaurants by delivery fee range
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = :status AND r.deliveryFee BETWEEN :minFee AND :maxFee")
    Page<Restaurant> findByDeliveryFeeBetween(@Param("minFee") BigDecimal minFee,
                                            @Param("maxFee") BigDecimal maxFee,
                                            @Param("status") RestaurantStatus status,
                                            Pageable pageable);

    /**
     * Find restaurants by estimated delivery time
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = :status AND r.estimatedDeliveryTime <= :maxTime")
    Page<Restaurant> findByEstimatedDeliveryTimeLessThanEqual(@Param("maxTime") Integer maxTime,
                                                            @Param("status") RestaurantStatus status,
                                                            Pageable pageable);

    /**
     * Count restaurants by status
     */
    long countByStatus(RestaurantStatus status);

    /**
     * Count restaurants by owner
     */
    long countByOwner(User owner);

    /**
     * Check if restaurant name exists for different restaurant
     */
    boolean existsByNameIgnoreCaseAndIdNot(String name, Long id);

    /**
     * Check if restaurant name exists
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Find restaurants with pending verification
     */
    @Query("SELECT r FROM Restaurant r WHERE r.status = 'ACTIVE' AND r.isVerified = false")
    Page<Restaurant> findPendingVerification(Pageable pageable);

    /**
     * Get restaurant statistics
     */
    @Query("SELECT " +
           "COUNT(r) as totalRestaurants, " +
           "COUNT(CASE WHEN r.status = 'ACTIVE' THEN 1 END) as activeRestaurants, " +
           "COUNT(CASE WHEN r.isVerified = true THEN 1 END) as verifiedRestaurants, " +
           "COUNT(CASE WHEN r.isFeatured = true THEN 1 END) as featuredRestaurants, " +
           "AVG(r.averageRating) as averageRating " +
           "FROM Restaurant r")
    Object[] getRestaurantStatistics();

    /**
     * Find restaurants by multiple filters
     */
    @Query("SELECT r FROM Restaurant r WHERE " +
           "(:status IS NULL OR r.status = :status) AND " +
           "(:isOpen IS NULL OR r.isOpen = :isOpen) AND " +
           "(:isVerified IS NULL OR r.isVerified = :isVerified) AND " +
           "(:isFeatured IS NULL OR r.isFeatured = :isFeatured) AND " +
           "(:city IS NULL OR LOWER(r.city) = LOWER(:city)) AND " +
           "(:minRating IS NULL OR r.averageRating >= :minRating) AND " +
           "(:maxDeliveryFee IS NULL OR r.deliveryFee <= :maxDeliveryFee)")
    Page<Restaurant> findByFilters(@Param("status") RestaurantStatus status,
                                 @Param("isOpen") Boolean isOpen,
                                 @Param("isVerified") Boolean isVerified,
                                 @Param("isFeatured") Boolean isFeatured,
                                 @Param("city") String city,
                                 @Param("minRating") BigDecimal minRating,
                                 @Param("maxDeliveryFee") BigDecimal maxDeliveryFee,
                                 Pageable pageable);
}
