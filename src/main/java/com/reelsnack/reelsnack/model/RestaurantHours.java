package com.reelsnack.reelsnack.model;

import com.reelsnack.reelsnack.model.enums.DayOfWeek;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Restaurant hours entity representing operating hours for each day of the week.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "restaurant_hours",
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_restaurant_day", columnNames = {"restaurant_id", "day_of_week"})
    },
    indexes = {
        @Index(name = "idx_restaurant_hours_restaurant", columnList = "restaurant_id"),
        @Index(name = "idx_restaurant_hours_day", columnList = "day_of_week"),
        @Index(name = "idx_restaurant_hours_open", columnList = "is_open")
    }
)
public class RestaurantHours {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "restaurant_id", nullable = false)
    private Restaurant restaurant;

    @Enumerated(EnumType.STRING)
    @Column(name = "day_of_week", nullable = false)
    private DayOfWeek dayOfWeek;

    @Column(name = "is_open", nullable = false)
    @Builder.Default
    private Boolean isOpen = true;

    @Column(name = "opening_time")
    private LocalTime openingTime;

    @Column(name = "closing_time")
    private LocalTime closingTime;

    @Column(name = "break_start_time")
    private LocalTime breakStartTime;

    @Column(name = "break_end_time")
    private LocalTime breakEndTime;

    @Column(name = "is_24_hours", nullable = false)
    @Builder.Default
    private Boolean is24Hours = false;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Business methods

    /**
     * Check if restaurant is open at a specific time on this day
     */
    public boolean isOpenAt(LocalTime time) {
        if (!isOpen) {
            return false;
        }

        if (is24Hours) {
            return true;
        }

        if (openingTime == null || closingTime == null) {
            return false;
        }

        // Handle break time
        if (breakStartTime != null && breakEndTime != null) {
            if (time.isAfter(breakStartTime) && time.isBefore(breakEndTime)) {
                return false;
            }
        }

        // Handle normal hours
        if (closingTime.isAfter(openingTime)) {
            // Same day (e.g., 9:00 AM - 10:00 PM)
            return time.isAfter(openingTime) && time.isBefore(closingTime);
        } else {
            // Crosses midnight (e.g., 10:00 PM - 2:00 AM)
            return time.isAfter(openingTime) || time.isBefore(closingTime);
        }
    }

    /**
     * Check if this represents a valid operating hours configuration
     */
    public boolean isValidConfiguration() {
        if (!isOpen) {
            return true; // Closed day is always valid
        }

        if (is24Hours) {
            return true; // 24-hour operation is valid
        }

        if (openingTime == null || closingTime == null) {
            return false; // Must have both opening and closing times
        }

        // If break times are set, they must be valid
        if (breakStartTime != null || breakEndTime != null) {
            if (breakStartTime == null || breakEndTime == null) {
                return false; // Both break times must be set
            }

            // Break must be within operating hours
            if (!isOpenAt(breakStartTime) || !isOpenAt(breakEndTime)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get formatted hours string for display
     */
    public String getFormattedHours() {
        if (!isOpen) {
            return "Closed";
        }

        if (is24Hours) {
            return "24 Hours";
        }

        if (openingTime == null || closingTime == null) {
            return "Hours not set";
        }

        StringBuilder hours = new StringBuilder();
        hours.append(openingTime.toString()).append(" - ").append(closingTime.toString());

        if (breakStartTime != null && breakEndTime != null) {
            hours.append(" (Break: ").append(breakStartTime.toString())
                  .append(" - ").append(breakEndTime.toString()).append(")");
        }

        return hours.toString();
    }
}
