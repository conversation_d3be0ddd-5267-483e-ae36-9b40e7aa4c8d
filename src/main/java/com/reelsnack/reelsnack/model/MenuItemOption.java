package com.reelsnack.reelsnack.model;

import com.reelsnack.reelsnack.model.enums.OptionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Menu item option entity representing customization options for menu items.
 * Examples: Size (Small, Medium, Large), Extras (Extra Cheese, Bacon), etc.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "menu_item_options",
    indexes = {
        @Index(name = "idx_menu_item_option_item", columnList = "menu_item_id"),
        @Index(name = "idx_menu_item_option_type", columnList = "option_type"),
        @Index(name = "idx_menu_item_option_sort", columnList = "menu_item_id, sort_order")
    }
)
public class MenuItemOption {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false)
    private String name;

    @Size(max = 500)
    @Column(name = "description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_item_id", nullable = false)
    private MenuItem menuItem;

    @Enumerated(EnumType.STRING)
    @Column(name = "option_type", nullable = false)
    private OptionType optionType;

    @Column(name = "is_required", nullable = false)
    @Builder.Default
    private Boolean isRequired = false;

    @Column(name = "is_multiple_choice", nullable = false)
    @Builder.Default
    private Boolean isMultipleChoice = false;

    @Min(0)
    @Column(name = "min_selections")
    @Builder.Default
    private Integer minSelections = 0;

    @Min(1)
    @Column(name = "max_selections")
    @Builder.Default
    private Integer maxSelections = 1;

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "option", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("sortOrder ASC, name ASC")
    @Builder.Default
    private List<MenuItemOptionValue> values = new ArrayList<>();

    // Business methods
    public void addValue(MenuItemOptionValue value) {
        values.add(value);
        value.setOption(this);
    }

    public void removeValue(MenuItemOptionValue value) {
        values.remove(value);
        value.setOption(null);
    }

    /**
     * Get count of available option values
     */
    public long getAvailableValueCount() {
        return values.stream()
                .filter(value -> Boolean.TRUE.equals(value.getIsAvailable()))
                .count();
    }

    /**
     * Check if option has any available values
     */
    public boolean hasAvailableValues() {
        return values.stream()
                .anyMatch(value -> Boolean.TRUE.equals(value.getIsAvailable()));
    }

    /**
     * Get the minimum additional price for this option
     */
    public BigDecimal getMinAdditionalPrice() {
        return values.stream()
                .filter(value -> Boolean.TRUE.equals(value.getIsAvailable()))
                .map(MenuItemOptionValue::getAdditionalPrice)
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Get the maximum additional price for this option
     */
    public BigDecimal getMaxAdditionalPrice() {
        return values.stream()
                .filter(value -> Boolean.TRUE.equals(value.getIsAvailable()))
                .map(MenuItemOptionValue::getAdditionalPrice)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }
}
