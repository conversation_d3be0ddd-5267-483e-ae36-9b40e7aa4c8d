package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Represents a content bookmark/save action by a user.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "content_bookmarks",
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_user_content_bookmark", columnNames = {"user_id", "content_id"})
    },
    indexes = {
        @Index(name = "idx_content_bookmarks_content", columnList = "content_id"),
        @Index(name = "idx_content_bookmarks_user", columnList = "user_id"),
        @Index(name = "idx_content_bookmarks_created_at", columnList = "created_at DESC"),
        @Index(name = "idx_content_bookmarks_user_created", columnList = "user_id, created_at DESC")
    }
)
public class ContentBookmark {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "content_id", nullable = false, foreignKey = @ForeignKey(name = "fk_content_bookmark_content"))
    private Content content;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "fk_content_bookmark_user"))
    private User user;

    @Column(name = "bookmark_collection", length = 100)
    private String bookmarkCollection; // Optional collection name like "Favorites", "Watch Later", etc.

    @Column(name = "notes", length = 500)
    private String notes; // Optional personal notes about the bookmarked content

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // ===== Business Methods =====

    /**
     * Gets the content ID for convenience.
     */
    public Long getContentId() {
        return content != null ? content.getId() : null;
    }

    /**
     * Gets the user ID for convenience.
     */
    public Long getUserId() {
        return user != null ? user.getId() : null;
    }

    /**
     * Gets the username for convenience.
     */
    public String getUsername() {
        return user != null ? user.getUsername() : null;
    }

    /**
     * Gets the content title for convenience.
     */
    public String getContentTitle() {
        return content != null ? content.getTitle() : null;
    }
}
