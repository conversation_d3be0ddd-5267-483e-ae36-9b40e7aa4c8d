package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Menu category entity representing a category of menu items in a restaurant.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "menu_categories",
    indexes = {
        @Index(name = "idx_menu_category_restaurant", columnList = "restaurant_id"),
        @Index(name = "idx_menu_category_active", columnList = "is_active"),
        @Index(name = "idx_menu_category_sort", columnList = "restaurant_id, sort_order")
    }
)
public class MenuCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false)
    private String name;

    @Size(max = 500)
    @Column(name = "description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "restaurant_id", nullable = false)
    private Restaurant restaurant;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @Size(max = 255)
    @Column(name = "image_url")
    private String imageUrl;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("sortOrder ASC, name ASC")
    @Builder.Default
    private List<MenuItem> menuItems = new ArrayList<>();

    // Business methods
    public void addMenuItem(MenuItem item) {
        menuItems.add(item);
        item.setCategory(this);
    }

    public void removeMenuItem(MenuItem item) {
        menuItems.remove(item);
        item.setCategory(null);
    }

    /**
     * Get count of active menu items in this category
     */
    public long getActiveItemCount() {
        return menuItems.stream()
                .filter(item -> Boolean.TRUE.equals(item.getIsAvailable()))
                .count();
    }

    /**
     * Check if category has any available items
     */
    public boolean hasAvailableItems() {
        return menuItems.stream()
                .anyMatch(item -> Boolean.TRUE.equals(item.getIsAvailable()));
    }
}
