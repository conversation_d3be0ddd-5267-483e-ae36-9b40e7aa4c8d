package com.reelsnack.reelsnack.model.enums;

/**
 * Enum representing days of the week for restaurant operating hours.
 */
public enum DayOfWeek {
    MONDAY(1, "Monday"),
    TUESDAY(2, "Tuesday"),
    WEDNESDAY(3, "Wednesday"),
    THURSDAY(4, "Thursday"),
    FRIDAY(5, "Friday"),
    SATURDAY(6, "Saturday"),
    SUNDAY(7, "Sunday");

    private final int dayNumber;
    private final String displayName;

    DayOfWeek(int dayNumber, String displayName) {
        this.dayNumber = dayNumber;
        this.displayName = displayName;
    }

    public int getDayNumber() {
        return dayNumber;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get DayOfWeek from Java's DayOfWeek
     */
    public static DayOfWeek fromJavaDayOfWeek(java.time.DayOfWeek javaDayOfWeek) {
        switch (javaDayOfWeek) {
            case MONDAY: return MONDAY;
            case TUESDAY: return TUESDAY;
            case WEDNESDAY: return WEDNESDAY;
            case THURSDAY: return THURSDAY;
            case FRIDAY: return FRIDAY;
            case SATURDAY: return SATURDAY;
            case SUNDAY: return SUNDAY;
            default: throw new IllegalArgumentException("Unknown day of week: " + javaDayOfWeek);
        }
    }

    /**
     * Convert to Java's DayOfWeek
     */
    public java.time.DayOfWeek toJavaDayOfWeek() {
        switch (this) {
            case MONDAY: return java.time.DayOfWeek.MONDAY;
            case TUESDAY: return java.time.DayOfWeek.TUESDAY;
            case WEDNESDAY: return java.time.DayOfWeek.WEDNESDAY;
            case THURSDAY: return java.time.DayOfWeek.THURSDAY;
            case FRIDAY: return java.time.DayOfWeek.FRIDAY;
            case SATURDAY: return java.time.DayOfWeek.SATURDAY;
            case SUNDAY: return java.time.DayOfWeek.SUNDAY;
            default: throw new IllegalArgumentException("Unknown day of week: " + this);
        }
    }

    /**
     * Check if this is a weekend day
     */
    public boolean isWeekend() {
        return this == SATURDAY || this == SUNDAY;
    }

    /**
     * Check if this is a weekday
     */
    public boolean isWeekday() {
        return !isWeekend();
    }
}
