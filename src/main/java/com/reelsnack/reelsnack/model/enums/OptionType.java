package com.reelsnack.reelsnack.model.enums;

/**
 * Enum representing different types of menu item options.
 */
public enum OptionType {
    /**
     * Size options (Small, Medium, Large, etc.)
     */
    SIZE,
    
    /**
     * Extra ingredients or add-ons
     */
    EXTRAS,
    
    /**
     * Cooking preferences (Rare, Medium, Well-done, etc.)
     */
    COOKING_PREFERENCE,
    
    /**
     * Spice level options
     */
    SPICE_LEVEL,
    
    /**
     * Sauce options
     */
    SAUCE,
    
    /**
     * Side dish options
     */
    SIDES,
    
    /**
     * Drink options
     */
    DRINKS,
    
    /**
     * Customization options (Remove ingredients, etc.)
     */
    CUSTOMIZATION,
    
    /**
     * Other miscellaneous options
     */
    OTHER
}
