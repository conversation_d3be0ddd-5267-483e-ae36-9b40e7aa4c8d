package com.reelsnack.reelsnack.model.enums;

/**
 * Enum representing the status of a restaurant in the system.
 */
public enum RestaurantStatus {
    /**
     * Restaurant application is pending review
     */
    PENDING,
    
    /**
     * Restaurant is active and can receive orders
     */
    ACTIVE,
    
    /**
     * Restaurant is temporarily closed but still visible
     */
    TEMPORARILY_CLOSED,
    
    /**
     * Restaurant is suspended by admin
     */
    SUSPENDED,
    
    /**
     * Restaurant account is deactivated
     */
    INACTIVE,
    
    /**
     * Restaurant application was rejected
     */
    REJECTED
}
