package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Menu item entity representing a food item in a restaurant's menu.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "menu_items",
    indexes = {
        @Index(name = "idx_menu_item_category", columnList = "category_id"),
        @Index(name = "idx_menu_item_available", columnList = "is_available"),
        @Index(name = "idx_menu_item_featured", columnList = "is_featured"),
        @Index(name = "idx_menu_item_price", columnList = "price"),
        @Index(name = "idx_menu_item_sort", columnList = "category_id, sort_order")
    }
)
public class MenuItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false)
    private String name;

    @Size(max = 1000)
    @Column(name = "description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private MenuCategory category;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = false)
    @Column(name = "price", nullable = false, precision = 8, scale = 2)
    private BigDecimal price;

    @DecimalMin(value = "0.0")
    @Column(name = "original_price", precision = 8, scale = 2)
    private BigDecimal originalPrice;

    @Column(name = "is_available", nullable = false)
    @Builder.Default
    private Boolean isAvailable = true;

    @Column(name = "is_featured", nullable = false)
    @Builder.Default
    private Boolean isFeatured = false;

    @Column(name = "is_vegetarian", nullable = false)
    @Builder.Default
    private Boolean isVegetarian = false;

    @Column(name = "is_vegan", nullable = false)
    @Builder.Default
    private Boolean isVegan = false;

    @Column(name = "is_gluten_free", nullable = false)
    @Builder.Default
    private Boolean isGlutenFree = false;

    @Column(name = "is_spicy", nullable = false)
    @Builder.Default
    private Boolean isSpicy = false;

    @Min(0)
    @Max(5)
    @Column(name = "spice_level")
    @Builder.Default
    private Integer spiceLevel = 0;

    @Min(0)
    @Column(name = "calories")
    private Integer calories;

    @Min(0)
    @Column(name = "preparation_time")
    @Builder.Default
    private Integer preparationTime = 15; // in minutes

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @Size(max = 255)
    @Column(name = "image_url")
    private String imageUrl;

    @Size(max = 500)
    @Column(name = "ingredients")
    private String ingredients; // JSON array as string

    @Size(max = 500)
    @Column(name = "allergens")
    private String allergens; // JSON array as string

    @Size(max = 500)
    @Column(name = "nutritional_info")
    private String nutritionalInfo; // JSON object as string

    @DecimalMin(value = "0.0")
    @DecimalMax(value = "5.0")
    @Column(name = "average_rating", precision = 3, scale = 2)
    @Builder.Default
    private BigDecimal averageRating = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "total_reviews")
    @Builder.Default
    private Integer totalReviews = 0;

    @Min(0)
    @Column(name = "total_orders")
    @Builder.Default
    private Integer totalOrders = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "menuItem", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private List<MenuItemOption> options = new ArrayList<>();

    // Business methods
    public void addOption(MenuItemOption option) {
        options.add(option);
        option.setMenuItem(this);
    }

    public void removeOption(MenuItemOption option) {
        options.remove(option);
        option.setMenuItem(null);
    }

    /**
     * Check if item is on sale (has original price higher than current price)
     */
    public boolean isOnSale() {
        return originalPrice != null && originalPrice.compareTo(price) > 0;
    }

    /**
     * Get discount percentage if item is on sale
     */
    public BigDecimal getDiscountPercentage() {
        if (!isOnSale()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = originalPrice.subtract(price);
        return discount.divide(originalPrice, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    /**
     * Update average rating based on new review
     */
    public void updateRating(BigDecimal newRating) {
        if (totalReviews == 0) {
            averageRating = newRating;
            totalReviews = 1;
        } else {
            BigDecimal totalRatingPoints = averageRating.multiply(BigDecimal.valueOf(totalReviews));
            totalRatingPoints = totalRatingPoints.add(newRating);
            totalReviews++;
            averageRating = totalRatingPoints.divide(BigDecimal.valueOf(totalReviews), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * Increment order count
     */
    public void incrementOrderCount() {
        totalOrders++;
    }

    /**
     * Get the restaurant this item belongs to
     */
    public Restaurant getRestaurant() {
        return category != null ? category.getRestaurant() : null;
    }
}
