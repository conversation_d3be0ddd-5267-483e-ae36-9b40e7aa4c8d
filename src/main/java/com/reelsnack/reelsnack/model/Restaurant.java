package com.reelsnack.reelsnack.model;

import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Restaurant entity representing a restaurant business on the platform.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "restaurants",
    indexes = {
        @Index(name = "idx_restaurant_owner", columnList = "owner_id"),
        @Index(name = "idx_restaurant_status", columnList = "status"),
        @Index(name = "idx_restaurant_location", columnList = "latitude, longitude"),
        @Index(name = "idx_restaurant_rating", columnList = "average_rating DESC"),
        @Index(name = "idx_restaurant_created", columnList = "created_at DESC")
    }
)
public class Restaurant {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false)
    private String name;

    @Size(max = 500)
    @Column(name = "description")
    private String description;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", nullable = false)
    private User owner;

    @NotBlank
    @Size(max = 200)
    @Column(name = "address", nullable = false)
    private String address;

    @Size(max = 100)
    @Column(name = "city")
    private String city;

    @Size(max = 50)
    @Column(name = "state")
    private String state;

    @Size(max = 20)
    @Column(name = "postal_code")
    private String postalCode;

    @Size(max = 50)
    @Column(name = "country")
    private String country;

    @DecimalMin(value = "-90.0")
    @DecimalMax(value = "90.0")
    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;

    @DecimalMin(value = "-180.0")
    @DecimalMax(value = "180.0")
    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$")
    @Size(max = 20)
    @Column(name = "phone_number")
    private String phoneNumber;

    @Email
    @Size(max = 100)
    @Column(name = "email")
    private String email;

    @Size(max = 255)
    @Column(name = "website")
    private String website;

    @Size(max = 255)
    @Column(name = "logo_url")
    private String logoUrl;

    @Size(max = 255)
    @Column(name = "cover_image_url")
    private String coverImageUrl;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private RestaurantStatus status = RestaurantStatus.PENDING;

    @Column(name = "is_verified", nullable = false)
    @Builder.Default
    private Boolean isVerified = false;

    @Column(name = "is_featured", nullable = false)
    @Builder.Default
    private Boolean isFeatured = false;

    @Column(name = "is_open", nullable = false)
    @Builder.Default
    private Boolean isOpen = false;

    @DecimalMin(value = "0.0")
    @DecimalMax(value = "5.0")
    @Column(name = "average_rating", precision = 3, scale = 2)
    @Builder.Default
    private BigDecimal averageRating = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "total_reviews")
    @Builder.Default
    private Integer totalReviews = 0;

    @Min(0)
    @Column(name = "delivery_fee", precision = 8, scale = 2)
    @Builder.Default
    private BigDecimal deliveryFee = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "minimum_order", precision = 8, scale = 2)
    @Builder.Default
    private BigDecimal minimumOrder = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "estimated_delivery_time")
    @Builder.Default
    private Integer estimatedDeliveryTime = 30; // in minutes

    @Min(0)
    @DecimalMax(value = "50.0")
    @Column(name = "delivery_radius", precision = 5, scale = 2)
    @Builder.Default
    private BigDecimal deliveryRadius = BigDecimal.valueOf(5.0); // in kilometers

    @Column(name = "opening_time")
    private LocalTime openingTime;

    @Column(name = "closing_time")
    private LocalTime closingTime;

    @Size(max = 500)
    @Column(name = "cuisine_types")
    private String cuisineTypes; // JSON array as string

    @Size(max = 500)
    @Column(name = "special_features")
    private String specialFeatures; // JSON array as string

    @Size(max = 1000)
    @Column(name = "admin_notes")
    private String adminNotes;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "restaurant", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private List<MenuCategory> menuCategories = new ArrayList<>();

    @OneToMany(mappedBy = "restaurant", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private List<RestaurantHours> operatingHours = new ArrayList<>();

    // Business methods
    public void addMenuCategory(MenuCategory category) {
        menuCategories.add(category);
        category.setRestaurant(this);
    }

    public void removeMenuCategory(MenuCategory category) {
        menuCategories.remove(category);
        category.setRestaurant(null);
    }

    public void addOperatingHours(RestaurantHours hours) {
        operatingHours.add(hours);
        hours.setRestaurant(this);
    }

    public void removeOperatingHours(RestaurantHours hours) {
        operatingHours.remove(hours);
        hours.setRestaurant(null);
    }

    /**
     * Check if restaurant is currently open based on operating hours
     */
    public boolean isCurrentlyOpen() {
        if (!isOpen || status != RestaurantStatus.ACTIVE) {
            return false;
        }
        
        // Simple check - can be enhanced with day-specific hours
        LocalTime now = LocalTime.now();
        return openingTime != null && closingTime != null &&
               now.isAfter(openingTime) && now.isBefore(closingTime);
    }

    /**
     * Update average rating based on new review
     */
    public void updateRating(BigDecimal newRating) {
        if (totalReviews == 0) {
            averageRating = newRating;
            totalReviews = 1;
        } else {
            BigDecimal totalRatingPoints = averageRating.multiply(BigDecimal.valueOf(totalReviews));
            totalRatingPoints = totalRatingPoints.add(newRating);
            totalReviews++;
            averageRating = totalRatingPoints.divide(BigDecimal.valueOf(totalReviews), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * Check if restaurant delivers to given coordinates
     */
    public boolean deliversTo(BigDecimal targetLat, BigDecimal targetLng) {
        if (latitude == null || longitude == null || targetLat == null || targetLng == null) {
            return false;
        }
        
        // Simple distance calculation (can be enhanced with proper geospatial queries)
        double distance = calculateDistance(
            latitude.doubleValue(), longitude.doubleValue(),
            targetLat.doubleValue(), targetLng.doubleValue()
        );
        
        return distance <= deliveryRadius.doubleValue();
    }

    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Radius of the earth in km
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
}
