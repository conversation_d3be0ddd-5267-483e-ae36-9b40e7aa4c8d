package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Represents a content share action by a user.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "content_shares",
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_user_content_share", columnNames = {"user_id", "content_id"})
    },
    indexes = {
        @Index(name = "idx_content_shares_content", columnList = "content_id"),
        @Index(name = "idx_content_shares_user", columnList = "user_id"),
        @Index(name = "idx_content_shares_created_at", columnList = "created_at DESC"),
        @Index(name = "idx_content_shares_content_created", columnList = "content_id, created_at DESC")
    }
)
public class ContentShare {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "content_id", nullable = false, foreignKey = @ForeignKey(name = "fk_content_share_content"))
    private Content content;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "fk_content_share_user"))
    private User user;

    @Column(name = "share_platform", length = 50)
    private String sharePlatform; // e.g., "FACEBOOK", "TWITTER", "WHATSAPP", "COPY_LINK"

    @Column(name = "share_message", length = 500)
    private String shareMessage; // Optional message when sharing

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // ===== Business Methods =====

    /**
     * Gets the content ID for convenience.
     */
    public Long getContentId() {
        return content != null ? content.getId() : null;
    }

    /**
     * Gets the user ID for convenience.
     */
    public Long getUserId() {
        return user != null ? user.getId() : null;
    }

    /**
     * Gets the username for convenience.
     */
    public String getUsername() {
        return user != null ? user.getUsername() : null;
    }

    /**
     * Gets the content title for convenience.
     */
    public String getContentTitle() {
        return content != null ? content.getTitle() : null;
    }
}
