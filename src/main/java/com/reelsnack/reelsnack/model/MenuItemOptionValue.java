package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Menu item option value entity representing specific values for menu item options.
 * Examples: For Size option - Small (+$0), Medium (+$2), Large (+$4)
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "menu_item_option_values",
    indexes = {
        @Index(name = "idx_menu_item_option_value_option", columnList = "option_id"),
        @Index(name = "idx_menu_item_option_value_available", columnList = "is_available"),
        @Index(name = "idx_menu_item_option_value_sort", columnList = "option_id, sort_order")
    }
)
public class MenuItemOptionValue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(name = "name", nullable = false)
    private String name;

    @Size(max = 500)
    @Column(name = "description")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "option_id", nullable = false)
    private MenuItemOption option;

    @NotNull
    @DecimalMin(value = "0.0")
    @Column(name = "additional_price", nullable = false, precision = 8, scale = 2)
    @Builder.Default
    private BigDecimal additionalPrice = BigDecimal.ZERO;

    @Column(name = "is_available", nullable = false)
    @Builder.Default
    private Boolean isAvailable = true;

    @Column(name = "is_default", nullable = false)
    @Builder.Default
    private Boolean isDefault = false;

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @Min(0)
    @Column(name = "additional_calories")
    @Builder.Default
    private Integer additionalCalories = 0;

    @Min(0)
    @Column(name = "additional_prep_time")
    @Builder.Default
    private Integer additionalPrepTime = 0; // in minutes

    @Size(max = 255)
    @Column(name = "image_url")
    private String imageUrl;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Business methods

    /**
     * Check if this option value is free (no additional cost)
     */
    public boolean isFree() {
        return additionalPrice.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Get the total price impact including base item price
     */
    public BigDecimal getTotalPriceWith(BigDecimal basePrice) {
        return basePrice.add(additionalPrice);
    }

    /**
     * Get the menu item this option value belongs to
     */
    public MenuItem getMenuItem() {
        return option != null ? option.getMenuItem() : null;
    }

    /**
     * Get the restaurant this option value belongs to
     */
    public Restaurant getRestaurant() {
        MenuItem menuItem = getMenuItem();
        return menuItem != null ? menuItem.getRestaurant() : null;
    }
}
