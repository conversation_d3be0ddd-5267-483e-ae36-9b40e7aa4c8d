package com.reelsnack.reelsnack.mapper;

import com.reelsnack.reelsnack.dto.request.CreateContentRequest;
import com.reelsnack.reelsnack.dto.response.ContentResponse;
import com.reelsnack.reelsnack.model.Content;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = {UserMapper.class}
)
public interface ContentMapper {
    ContentMapper INSTANCE = Mappers.getMapper(ContentMapper.class);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "viewCount", expression = "java(0L)")
    @Mapping(target = "likeCount", expression = "java(0L)")
    @Mapping(target = "commentCount", expression = "java(0L)")
    @Mapping(target = "shareCount", expression = "java(0L)")
    @Mapping(target = "isPublic", constant = "true")
    @Mapping(target = "isPromoted", constant = "false")
    @Mapping(target = "isDeleted", constant = "false")
    Content toEntity(CreateContentRequest request);
    
    @Mapping(target = "creator", source = "creator")
    @Mapping(target = "mediaUrl", source = "mediaUrl")
    @Mapping(target = "thumbnailUrl", source = "thumbnailUrl")
    @Mapping(target = "tags", source = "tags")
    @Mapping(target = "duration", source = "duration")
    @Mapping(target = "isLiked", constant = "false") // Default value
    @Mapping(target = "isBookmarked", constant = "false")  // Default value
    @Mapping(target = "shareUrl", expression = "java(\"/api/v1/content/\" + content.getId() + \"/share\")")
    @Mapping(target = "mimeType", ignore = true) // These would need to be set based on actual file data
    @Mapping(target = "fileSize", ignore = true)
    @Mapping(target = "resolution", ignore = true)
    @Mapping(target = "aspectRatio", ignore = true)
    ContentResponse toDto(Content content);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "viewCount", ignore = true)
    @Mapping(target = "likeCount", ignore = true)
    @Mapping(target = "commentCount", ignore = true)
    @Mapping(target = "shareCount", ignore = true)
    void updateEntity(@MappingTarget Content entity, CreateContentRequest request);
}
