package com.reelsnack.reelsnack.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reelsnack.reelsnack.dto.request.RestaurantRequest;
import com.reelsnack.reelsnack.dto.response.RestaurantResponse;
import com.reelsnack.reelsnack.model.Restaurant;
import com.reelsnack.reelsnack.model.RestaurantHours;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Restaurant entities and DTOs.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RestaurantMapper {

    private final ObjectMapper objectMapper;

    /**
     * Convert CreateRestaurantRequest to Restaurant entity
     */
    public Restaurant toEntity(RestaurantRequest.CreateRestaurantRequest request) {
        return Restaurant.builder()
                .name(request.getName())
                .description(request.getDescription())
                .address(request.getAddress())
                .city(request.getCity())
                .state(request.getState())
                .postalCode(request.getPostalCode())
                .country(request.getCountry())
                .latitude(request.getLatitude())
                .longitude(request.getLongitude())
                .phoneNumber(request.getPhoneNumber())
                .email(request.getEmail())
                .website(request.getWebsite())
                .deliveryFee(request.getDeliveryFee() != null ? request.getDeliveryFee() : BigDecimal.ZERO)
                .minimumOrder(request.getMinimumOrder() != null ? request.getMinimumOrder() : BigDecimal.ZERO)
                .estimatedDeliveryTime(request.getEstimatedDeliveryTime() != null ? request.getEstimatedDeliveryTime() : 30)
                .deliveryRadius(request.getDeliveryRadius() != null ? request.getDeliveryRadius() : BigDecimal.valueOf(5.0))
                .openingTime(request.getOpeningTime())
                .closingTime(request.getClosingTime())
                .cuisineTypes(listToJson(request.getCuisineTypes()))
                .specialFeatures(listToJson(request.getSpecialFeatures()))
                .build();
    }

    /**
     * Update Restaurant entity from UpdateRestaurantRequest
     */
    public void updateEntityFromRequest(Restaurant restaurant, RestaurantRequest.UpdateRestaurantRequest request) {
        restaurant.setName(request.getName());
        restaurant.setDescription(request.getDescription());
        restaurant.setAddress(request.getAddress());
        restaurant.setCity(request.getCity());
        restaurant.setState(request.getState());
        restaurant.setPostalCode(request.getPostalCode());
        restaurant.setCountry(request.getCountry());
        restaurant.setLatitude(request.getLatitude());
        restaurant.setLongitude(request.getLongitude());
        restaurant.setPhoneNumber(request.getPhoneNumber());
        restaurant.setEmail(request.getEmail());
        restaurant.setWebsite(request.getWebsite());
        restaurant.setDeliveryFee(request.getDeliveryFee());
        restaurant.setMinimumOrder(request.getMinimumOrder());
        restaurant.setEstimatedDeliveryTime(request.getEstimatedDeliveryTime());
        restaurant.setDeliveryRadius(request.getDeliveryRadius());
        restaurant.setOpeningTime(request.getOpeningTime());
        restaurant.setClosingTime(request.getClosingTime());
        restaurant.setCuisineTypes(listToJson(request.getCuisineTypes()));
        restaurant.setSpecialFeatures(listToJson(request.getSpecialFeatures()));
    }

    /**
     * Convert Restaurant entity to RestaurantResponse
     */
    public RestaurantResponse toResponse(Restaurant restaurant) {
        return RestaurantResponse.builder()
                .id(restaurant.getId())
                .name(restaurant.getName())
                .description(restaurant.getDescription())
                .owner(toOwnerInfo(restaurant))
                .address(restaurant.getAddress())
                .city(restaurant.getCity())
                .state(restaurant.getState())
                .postalCode(restaurant.getPostalCode())
                .country(restaurant.getCountry())
                .latitude(restaurant.getLatitude())
                .longitude(restaurant.getLongitude())
                .phoneNumber(restaurant.getPhoneNumber())
                .email(restaurant.getEmail())
                .website(restaurant.getWebsite())
                .logoUrl(restaurant.getLogoUrl())
                .coverImageUrl(restaurant.getCoverImageUrl())
                .status(restaurant.getStatus())
                .isVerified(restaurant.getIsVerified())
                .isFeatured(restaurant.getIsFeatured())
                .isOpen(restaurant.getIsOpen())
                .isCurrentlyOpen(restaurant.isCurrentlyOpen())
                .averageRating(restaurant.getAverageRating())
                .totalReviews(restaurant.getTotalReviews())
                .deliveryFee(restaurant.getDeliveryFee())
                .minimumOrder(restaurant.getMinimumOrder())
                .estimatedDeliveryTime(restaurant.getEstimatedDeliveryTime())
                .deliveryRadius(restaurant.getDeliveryRadius())
                .openingTime(restaurant.getOpeningTime())
                .closingTime(restaurant.getClosingTime())
                .cuisineTypes(jsonToList(restaurant.getCuisineTypes()))
                .specialFeatures(jsonToList(restaurant.getSpecialFeatures()))
                .createdAt(restaurant.getCreatedAt())
                .updatedAt(restaurant.getUpdatedAt())
                .build();
    }

    /**
     * Convert Restaurant entity to RestaurantSummary
     */
    public RestaurantResponse.RestaurantSummary toSummary(Restaurant restaurant) {
        return RestaurantResponse.RestaurantSummary.builder()
                .id(restaurant.getId())
                .name(restaurant.getName())
                .logoUrl(restaurant.getLogoUrl())
                .coverImageUrl(restaurant.getCoverImageUrl())
                .status(restaurant.getStatus())
                .isOpen(restaurant.getIsOpen())
                .isCurrentlyOpen(restaurant.isCurrentlyOpen())
                .averageRating(restaurant.getAverageRating())
                .totalReviews(restaurant.getTotalReviews())
                .deliveryFee(restaurant.getDeliveryFee())
                .minimumOrder(restaurant.getMinimumOrder())
                .estimatedDeliveryTime(restaurant.getEstimatedDeliveryTime())
                .city(restaurant.getCity())
                .cuisineTypes(jsonToList(restaurant.getCuisineTypes()))
                .build();
    }

    /**
     * Convert Restaurant entity to RestaurantCard
     */
    public RestaurantResponse.RestaurantCard toCard(Restaurant restaurant) {
        return RestaurantResponse.RestaurantCard.builder()
                .id(restaurant.getId())
                .name(restaurant.getName())
                .logoUrl(restaurant.getLogoUrl())
                .coverImageUrl(restaurant.getCoverImageUrl())
                .isOpen(restaurant.getIsOpen())
                .isCurrentlyOpen(restaurant.isCurrentlyOpen())
                .averageRating(restaurant.getAverageRating())
                .totalReviews(restaurant.getTotalReviews())
                .deliveryFee(restaurant.getDeliveryFee())
                .estimatedDeliveryTime(restaurant.getEstimatedDeliveryTime())
                .cuisineTypes(jsonToList(restaurant.getCuisineTypes()))
                .isFeatured(restaurant.getIsFeatured())
                .build();
    }

    /**
     * Convert RestaurantHours to RestaurantHoursInfo
     */
    public RestaurantResponse.RestaurantHoursInfo toHoursInfo(RestaurantHours hours) {
        return RestaurantResponse.RestaurantHoursInfo.builder()
                .dayOfWeek(hours.getDayOfWeek().getDisplayName())
                .isOpen(hours.getIsOpen())
                .openingTime(hours.getOpeningTime())
                .closingTime(hours.getClosingTime())
                .breakStartTime(hours.getBreakStartTime())
                .breakEndTime(hours.getBreakEndTime())
                .is24Hours(hours.getIs24Hours())
                .formattedHours(hours.getFormattedHours())
                .build();
    }

    /**
     * Convert list of RestaurantHours to list of RestaurantHoursInfo
     */
    public List<RestaurantResponse.RestaurantHoursInfo> toHoursInfoList(List<RestaurantHours> hoursList) {
        if (hoursList == null) {
            return Collections.emptyList();
        }
        return hoursList.stream()
                .map(this::toHoursInfo)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of Restaurant entities to list of RestaurantResponse
     */
    public List<RestaurantResponse> toResponseList(List<Restaurant> restaurants) {
        if (restaurants == null) {
            return Collections.emptyList();
        }
        return restaurants.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of Restaurant entities to list of RestaurantCard
     */
    public List<RestaurantResponse.RestaurantCard> toCardList(List<Restaurant> restaurants) {
        if (restaurants == null) {
            return Collections.emptyList();
        }
        return restaurants.stream()
                .map(this::toCard)
                .collect(Collectors.toList());
    }

    // Helper methods

    private RestaurantResponse.OwnerInfo toOwnerInfo(Restaurant restaurant) {
        if (restaurant.getOwner() == null) {
            return null;
        }
        return RestaurantResponse.OwnerInfo.builder()
                .id(restaurant.getOwner().getId())
                .username(restaurant.getOwner().getUsername())
                .firstName(restaurant.getOwner().getFirstName())
                .lastName(restaurant.getOwner().getLastName())
                .email(restaurant.getOwner().getEmail())
                .phoneNumber(restaurant.getOwner().getPhoneNumber())
                .build();
    }

    private String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.warn("Failed to convert list to JSON: {}", e.getMessage());
            return null;
        }
    }

    private List<String> jsonToList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to convert JSON to list: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
}
