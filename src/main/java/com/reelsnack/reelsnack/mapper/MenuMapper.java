package com.reelsnack.reelsnack.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reelsnack.reelsnack.dto.request.MenuRequest;
import com.reelsnack.reelsnack.dto.response.MenuResponse;
import com.reelsnack.reelsnack.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Menu entities and DTOs.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MenuMapper {

    private final ObjectMapper objectMapper;

    // ===== MENU CATEGORY MAPPINGS =====

    /**
     * Convert CreateCategoryRequest to MenuCategory entity
     */
    public MenuCategory toEntity(MenuRequest.CreateCategoryRequest request) {
        return MenuCategory.builder()
                .name(request.getName())
                .description(request.getDescription())
                .sortOrder(request.getSortOrder() != null ? request.getSortOrder() : 0)
                .isActive(request.getIsActive() != null ? request.getIsActive() : true)
                .build();
    }

    /**
     * Update MenuCategory entity from UpdateCategoryRequest
     */
    public void updateCategoryFromRequest(MenuCategory category, MenuRequest.UpdateCategoryRequest request) {
        category.setName(request.getName());
        category.setDescription(request.getDescription());
        if (request.getSortOrder() != null) {
            category.setSortOrder(request.getSortOrder());
        }
        if (request.getIsActive() != null) {
            category.setIsActive(request.getIsActive());
        }
    }

    /**
     * Convert MenuCategory entity to CategoryResponse
     */
    public MenuResponse.CategoryResponse toCategoryResponse(MenuCategory category) {
        return MenuResponse.CategoryResponse.builder()
                .id(category.getId())
                .name(category.getName())
                .description(category.getDescription())
                .restaurantId(category.getRestaurant().getId())
                .restaurantName(category.getRestaurant().getName())
                .isActive(category.getIsActive())
                .sortOrder(category.getSortOrder())
                .imageUrl(category.getImageUrl())
                .activeItemCount(category.getActiveItemCount())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
    }

    /**
     * Convert MenuCategory entity to CategoryWithItems
     */
    public MenuResponse.CategoryWithItems toCategoryWithItems(MenuCategory category) {
        return MenuResponse.CategoryWithItems.builder()
                .id(category.getId())
                .name(category.getName())
                .description(category.getDescription())
                .isActive(category.getIsActive())
                .sortOrder(category.getSortOrder())
                .imageUrl(category.getImageUrl())
                .items(toItemSummaryList(category.getMenuItems()))
                .build();
    }

    // ===== MENU ITEM MAPPINGS =====

    /**
     * Convert CreateItemRequest to MenuItem entity
     */
    public MenuItem toEntity(MenuRequest.CreateItemRequest request) {
        return MenuItem.builder()
                .name(request.getName())
                .description(request.getDescription())
                .price(request.getPrice())
                .originalPrice(request.getOriginalPrice())
                .isAvailable(request.getIsAvailable() != null ? request.getIsAvailable() : true)
                .isFeatured(request.getIsFeatured() != null ? request.getIsFeatured() : false)
                .isVegetarian(request.getIsVegetarian() != null ? request.getIsVegetarian() : false)
                .isVegan(request.getIsVegan() != null ? request.getIsVegan() : false)
                .isGlutenFree(request.getIsGlutenFree() != null ? request.getIsGlutenFree() : false)
                .isSpicy(request.getIsSpicy() != null ? request.getIsSpicy() : false)
                .spiceLevel(request.getSpiceLevel() != null ? request.getSpiceLevel() : 0)
                .calories(request.getCalories())
                .preparationTime(request.getPreparationTime() != null ? request.getPreparationTime() : 15)
                .sortOrder(request.getSortOrder() != null ? request.getSortOrder() : 0)
                .ingredients(listToJson(request.getIngredients()))
                .allergens(listToJson(request.getAllergens()))
                .nutritionalInfo(request.getNutritionalInfo())
                .build();
    }

    /**
     * Update MenuItem entity from UpdateItemRequest
     */
    public void updateItemFromRequest(MenuItem item, MenuRequest.UpdateItemRequest request) {
        item.setName(request.getName());
        item.setDescription(request.getDescription());
        item.setPrice(request.getPrice());
        item.setOriginalPrice(request.getOriginalPrice());
        if (request.getIsAvailable() != null) {
            item.setIsAvailable(request.getIsAvailable());
        }
        if (request.getIsFeatured() != null) {
            item.setIsFeatured(request.getIsFeatured());
        }
        if (request.getIsVegetarian() != null) {
            item.setIsVegetarian(request.getIsVegetarian());
        }
        if (request.getIsVegan() != null) {
            item.setIsVegan(request.getIsVegan());
        }
        if (request.getIsGlutenFree() != null) {
            item.setIsGlutenFree(request.getIsGlutenFree());
        }
        if (request.getIsSpicy() != null) {
            item.setIsSpicy(request.getIsSpicy());
        }
        if (request.getSpiceLevel() != null) {
            item.setSpiceLevel(request.getSpiceLevel());
        }
        if (request.getCalories() != null) {
            item.setCalories(request.getCalories());
        }
        if (request.getPreparationTime() != null) {
            item.setPreparationTime(request.getPreparationTime());
        }
        if (request.getSortOrder() != null) {
            item.setSortOrder(request.getSortOrder());
        }
        item.setIngredients(listToJson(request.getIngredients()));
        item.setAllergens(listToJson(request.getAllergens()));
        item.setNutritionalInfo(request.getNutritionalInfo());
    }

    /**
     * Convert MenuItem entity to ItemResponse
     */
    public MenuResponse.ItemResponse toItemResponse(MenuItem item) {
        return MenuResponse.ItemResponse.builder()
                .id(item.getId())
                .name(item.getName())
                .description(item.getDescription())
                .categoryId(item.getCategory().getId())
                .categoryName(item.getCategory().getName())
                .restaurantId(item.getRestaurant().getId())
                .restaurantName(item.getRestaurant().getName())
                .price(item.getPrice())
                .originalPrice(item.getOriginalPrice())
                .isOnSale(item.isOnSale())
                .discountPercentage(item.getDiscountPercentage())
                .isAvailable(item.getIsAvailable())
                .isFeatured(item.getIsFeatured())
                .isVegetarian(item.getIsVegetarian())
                .isVegan(item.getIsVegan())
                .isGlutenFree(item.getIsGlutenFree())
                .isSpicy(item.getIsSpicy())
                .spiceLevel(item.getSpiceLevel())
                .calories(item.getCalories())
                .preparationTime(item.getPreparationTime())
                .sortOrder(item.getSortOrder())
                .imageUrl(item.getImageUrl())
                .ingredients(jsonToList(item.getIngredients()))
                .allergens(jsonToList(item.getAllergens()))
                .nutritionalInfo(item.getNutritionalInfo())
                .averageRating(item.getAverageRating())
                .totalReviews(item.getTotalReviews())
                .totalOrders(item.getTotalOrders())
                .options(toOptionResponseList(item.getOptions()))
                .createdAt(item.getCreatedAt())
                .updatedAt(item.getUpdatedAt())
                .build();
    }

    /**
     * Convert MenuItem entity to ItemSummary
     */
    public MenuResponse.ItemSummary toItemSummary(MenuItem item) {
        BigDecimal minPrice = calculateMinPrice(item);
        BigDecimal maxPrice = calculateMaxPrice(item);
        
        return MenuResponse.ItemSummary.builder()
                .id(item.getId())
                .name(item.getName())
                .description(item.getDescription())
                .price(item.getPrice())
                .originalPrice(item.getOriginalPrice())
                .isOnSale(item.isOnSale())
                .discountPercentage(item.getDiscountPercentage())
                .isAvailable(item.getIsAvailable())
                .isFeatured(item.getIsFeatured())
                .isVegetarian(item.getIsVegetarian())
                .isVegan(item.getIsVegan())
                .isGlutenFree(item.getIsGlutenFree())
                .isSpicy(item.getIsSpicy())
                .spiceLevel(item.getSpiceLevel())
                .imageUrl(item.getImageUrl())
                .averageRating(item.getAverageRating())
                .totalReviews(item.getTotalReviews())
                .hasOptions(!item.getOptions().isEmpty())
                .minPrice(minPrice)
                .maxPrice(maxPrice)
                .build();
    }

    /**
     * Convert MenuItem entity to ItemCard
     */
    public MenuResponse.ItemCard toItemCard(MenuItem item) {
        return MenuResponse.ItemCard.builder()
                .id(item.getId())
                .name(item.getName())
                .price(item.getPrice())
                .originalPrice(item.getOriginalPrice())
                .isOnSale(item.isOnSale())
                .isAvailable(item.getIsAvailable())
                .isFeatured(item.getIsFeatured())
                .isVegetarian(item.getIsVegetarian())
                .isVegan(item.getIsVegan())
                .isGlutenFree(item.getIsGlutenFree())
                .isSpicy(item.getIsSpicy())
                .imageUrl(item.getImageUrl())
                .averageRating(item.getAverageRating())
                .totalReviews(item.getTotalReviews())
                .restaurantName(item.getRestaurant().getName())
                .build();
    }

    // ===== MENU OPTION MAPPINGS =====

    /**
     * Convert MenuItemOption entity to OptionResponse
     */
    public MenuResponse.OptionResponse toOptionResponse(MenuItemOption option) {
        return MenuResponse.OptionResponse.builder()
                .id(option.getId())
                .name(option.getName())
                .description(option.getDescription())
                .menuItemId(option.getMenuItem().getId())
                .optionType(option.getOptionType())
                .isRequired(option.getIsRequired())
                .isMultipleChoice(option.getIsMultipleChoice())
                .minSelections(option.getMinSelections())
                .maxSelections(option.getMaxSelections())
                .sortOrder(option.getSortOrder())
                .values(toOptionValueResponseList(option.getValues()))
                .createdAt(option.getCreatedAt())
                .updatedAt(option.getUpdatedAt())
                .build();
    }

    /**
     * Convert MenuItemOptionValue entity to OptionValueResponse
     */
    public MenuResponse.OptionValueResponse toOptionValueResponse(MenuItemOptionValue value) {
        return MenuResponse.OptionValueResponse.builder()
                .id(value.getId())
                .name(value.getName())
                .description(value.getDescription())
                .optionId(value.getOption().getId())
                .additionalPrice(value.getAdditionalPrice())
                .isAvailable(value.getIsAvailable())
                .isDefault(value.getIsDefault())
                .isFree(value.isFree())
                .sortOrder(value.getSortOrder())
                .additionalCalories(value.getAdditionalCalories())
                .additionalPrepTime(value.getAdditionalPrepTime())
                .imageUrl(value.getImageUrl())
                .createdAt(value.getCreatedAt())
                .updatedAt(value.getUpdatedAt())
                .build();
    }

    // ===== LIST CONVERSION METHODS =====

    public List<MenuResponse.CategoryResponse> toCategoryResponseList(List<MenuCategory> categories) {
        if (categories == null) return Collections.emptyList();
        return categories.stream().map(this::toCategoryResponse).collect(Collectors.toList());
    }

    public List<MenuResponse.CategoryWithItems> toCategoryWithItemsList(List<MenuCategory> categories) {
        if (categories == null) return Collections.emptyList();
        return categories.stream().map(this::toCategoryWithItems).collect(Collectors.toList());
    }

    public List<MenuResponse.ItemResponse> toItemResponseList(List<MenuItem> items) {
        if (items == null) return Collections.emptyList();
        return items.stream().map(this::toItemResponse).collect(Collectors.toList());
    }

    public List<MenuResponse.ItemSummary> toItemSummaryList(List<MenuItem> items) {
        if (items == null) return Collections.emptyList();
        return items.stream().map(this::toItemSummary).collect(Collectors.toList());
    }

    public List<MenuResponse.ItemCard> toItemCardList(List<MenuItem> items) {
        if (items == null) return Collections.emptyList();
        return items.stream().map(this::toItemCard).collect(Collectors.toList());
    }

    public List<MenuResponse.OptionResponse> toOptionResponseList(List<MenuItemOption> options) {
        if (options == null) return Collections.emptyList();
        return options.stream().map(this::toOptionResponse).collect(Collectors.toList());
    }

    public List<MenuResponse.OptionValueResponse> toOptionValueResponseList(List<MenuItemOptionValue> values) {
        if (values == null) return Collections.emptyList();
        return values.stream().map(this::toOptionValueResponse).collect(Collectors.toList());
    }

    // ===== HELPER METHODS =====

    private BigDecimal calculateMinPrice(MenuItem item) {
        BigDecimal basePrice = item.getPrice();
        BigDecimal minOptionPrice = item.getOptions().stream()
                .filter(option -> Boolean.TRUE.equals(option.getIsRequired()))
                .map(MenuItemOption::getMinAdditionalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return basePrice.add(minOptionPrice);
    }

    private BigDecimal calculateMaxPrice(MenuItem item) {
        BigDecimal basePrice = item.getPrice();
        BigDecimal maxOptionPrice = item.getOptions().stream()
                .map(MenuItemOption::getMaxAdditionalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return basePrice.add(maxOptionPrice);
    }

    private String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) return null;
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.warn("Failed to convert list to JSON: {}", e.getMessage());
            return null;
        }
    }

    private List<String> jsonToList(String json) {
        if (json == null || json.trim().isEmpty()) return Collections.emptyList();
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to convert JSON to list: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
}
