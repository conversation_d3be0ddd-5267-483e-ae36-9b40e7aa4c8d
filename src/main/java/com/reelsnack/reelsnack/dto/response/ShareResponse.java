package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for content sharing.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Content sharing response")
public class ShareResponse {

    @Schema(description = "Share ID", example = "123")
    private Long id;

    @Schema(description = "Content ID that was shared", example = "456")
    private Long contentId;

    @Schema(description = "Content title", example = "Amazing Pasta Recipe")
    private String contentTitle;

    @Schema(description = "Platform where content was shared", example = "FACEBOOK")
    private String sharePlatform;

    @Schema(description = "Share message", example = "Check out this amazing recipe!")
    private String shareMessage;

    @Schema(description = "Shareable URL for the content", example = "https://app.reelsnack.com/content/456")
    private String shareUrl;

    @Schema(description = "When the content was shared", example = "2023-12-01T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sharedAt;

    @Schema(description = "User who shared the content")
    private UserSummaryResponse sharedBy;

    /**
     * Simplified user information for share responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "User information in share context")
    public static class UserSummaryResponse {

        @Schema(description = "User ID", example = "123")
        private Long id;

        @Schema(description = "Username", example = "chef_maria")
        private String username;

        @Schema(description = "User's first name", example = "Maria")
        private String firstName;

        @Schema(description = "User's last name", example = "Rodriguez")
        private String lastName;

        @Schema(description = "User's profile picture URL", example = "https://example.com/profile.jpg")
        private String profilePictureUrl;
    }
}
