package com.reelsnack.reelsnack.dto.response;

import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Response DTOs for restaurant operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RestaurantResponse {
    
    private Long id;
    private String name;
    private String description;
    private OwnerInfo owner;
    private String address;
    private String city;
    private String state;
    private String postalCode;
    private String country;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String phoneNumber;
    private String email;
    private String website;
    private String logoUrl;
    private String coverImageUrl;
    private RestaurantStatus status;
    private Boolean isVerified;
    private Boolean isFeatured;
    private Boolean isOpen;
    private Boolean isCurrentlyOpen;
    private BigDecimal averageRating;
    private Integer totalReviews;
    private BigDecimal deliveryFee;
    private BigDecimal minimumOrder;
    private Integer estimatedDeliveryTime;
    private BigDecimal deliveryRadius;
    private LocalTime openingTime;
    private LocalTime closingTime;
    private List<String> cuisineTypes;
    private List<String> specialFeatures;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Nested classes for related data
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OwnerInfo {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;
        private String email;
        private String phoneNumber;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantSummary {
        private Long id;
        private String name;
        private String logoUrl;
        private String coverImageUrl;
        private RestaurantStatus status;
        private Boolean isOpen;
        private Boolean isCurrentlyOpen;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private BigDecimal deliveryFee;
        private BigDecimal minimumOrder;
        private Integer estimatedDeliveryTime;
        private String city;
        private List<String> cuisineTypes;
        private Boolean deliversToUser; // Calculated field
        private BigDecimal distanceFromUser; // Calculated field
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantCard {
        private Long id;
        private String name;
        private String logoUrl;
        private String coverImageUrl;
        private Boolean isOpen;
        private Boolean isCurrentlyOpen;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private BigDecimal deliveryFee;
        private Integer estimatedDeliveryTime;
        private List<String> cuisineTypes;
        private Boolean isFeatured;
        private Boolean deliversToUser;
        private BigDecimal distanceFromUser;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantStats {
        private Long totalRestaurants;
        private Long activeRestaurants;
        private Long verifiedRestaurants;
        private Long featuredRestaurants;
        private BigDecimal averageRating;
        private Long totalMenuItems;
        private Long totalOrders;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantHoursInfo {
        private String dayOfWeek;
        private Boolean isOpen;
        private LocalTime openingTime;
        private LocalTime closingTime;
        private LocalTime breakStartTime;
        private LocalTime breakEndTime;
        private Boolean is24Hours;
        private String formattedHours;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantWithMenu {
        private Long id;
        private String name;
        private String description;
        private String logoUrl;
        private String coverImageUrl;
        private Boolean isOpen;
        private Boolean isCurrentlyOpen;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private BigDecimal deliveryFee;
        private BigDecimal minimumOrder;
        private Integer estimatedDeliveryTime;
        private String address;
        private String city;
        private String phoneNumber;
        private List<String> cuisineTypes;
        private List<String> specialFeatures;
        private List<RestaurantHoursInfo> operatingHours;
        private List<MenuResponse.CategoryWithItems> menuCategories;
    }
}
