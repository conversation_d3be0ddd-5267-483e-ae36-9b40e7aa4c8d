package com.reelsnack.reelsnack.dto.response;

import com.reelsnack.reelsnack.model.enums.OptionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTOs for menu operations.
 */
public class MenuResponse {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryResponse {
        private Long id;
        private String name;
        private String description;
        private Long restaurantId;
        private String restaurantName;
        private Boolean isActive;
        private Integer sortOrder;
        private String imageUrl;
        private Long activeItemCount;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryWithItems {
        private Long id;
        private String name;
        private String description;
        private Boolean isActive;
        private Integer sortOrder;
        private String imageUrl;
        private List<ItemSummary> items;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemResponse {
        private Long id;
        private String name;
        private String description;
        private Long categoryId;
        private String categoryName;
        private Long restaurantId;
        private String restaurantName;
        private BigDecimal price;
        private BigDecimal originalPrice;
        private Boolean isOnSale;
        private BigDecimal discountPercentage;
        private Boolean isAvailable;
        private Boolean isFeatured;
        private Boolean isVegetarian;
        private Boolean isVegan;
        private Boolean isGlutenFree;
        private Boolean isSpicy;
        private Integer spiceLevel;
        private Integer calories;
        private Integer preparationTime;
        private Integer sortOrder;
        private String imageUrl;
        private List<String> ingredients;
        private List<String> allergens;
        private String nutritionalInfo;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private Integer totalOrders;
        private List<OptionResponse> options;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemSummary {
        private Long id;
        private String name;
        private String description;
        private BigDecimal price;
        private BigDecimal originalPrice;
        private Boolean isOnSale;
        private BigDecimal discountPercentage;
        private Boolean isAvailable;
        private Boolean isFeatured;
        private Boolean isVegetarian;
        private Boolean isVegan;
        private Boolean isGlutenFree;
        private Boolean isSpicy;
        private Integer spiceLevel;
        private String imageUrl;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private Boolean hasOptions;
        private BigDecimal minPrice; // Base price + minimum option prices
        private BigDecimal maxPrice; // Base price + maximum option prices
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemCard {
        private Long id;
        private String name;
        private BigDecimal price;
        private BigDecimal originalPrice;
        private Boolean isOnSale;
        private Boolean isAvailable;
        private Boolean isFeatured;
        private Boolean isVegetarian;
        private Boolean isVegan;
        private Boolean isGlutenFree;
        private Boolean isSpicy;
        private String imageUrl;
        private BigDecimal averageRating;
        private Integer totalReviews;
        private String restaurantName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionResponse {
        private Long id;
        private String name;
        private String description;
        private Long menuItemId;
        private OptionType optionType;
        private Boolean isRequired;
        private Boolean isMultipleChoice;
        private Integer minSelections;
        private Integer maxSelections;
        private Integer sortOrder;
        private List<OptionValueResponse> values;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionValueResponse {
        private Long id;
        private String name;
        private String description;
        private Long optionId;
        private BigDecimal additionalPrice;
        private Boolean isAvailable;
        private Boolean isDefault;
        private Boolean isFree;
        private Integer sortOrder;
        private Integer additionalCalories;
        private Integer additionalPrepTime;
        private String imageUrl;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuStats {
        private Long totalCategories;
        private Long activeCategories;
        private Long totalItems;
        private Long availableItems;
        private Long featuredItems;
        private Long vegetarianItems;
        private BigDecimal averagePrice;
        private BigDecimal averageRating;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantMenu {
        private Long restaurantId;
        private String restaurantName;
        private List<CategoryWithItems> categories;
        private MenuStats stats;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PopularItem {
        private Long id;
        private String name;
        private BigDecimal price;
        private String imageUrl;
        private BigDecimal averageRating;
        private Integer totalOrders;
        private String categoryName;
        private String restaurantName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeaturedItem {
        private Long id;
        private String name;
        private String description;
        private BigDecimal price;
        private BigDecimal originalPrice;
        private Boolean isOnSale;
        private String imageUrl;
        private BigDecimal averageRating;
        private Boolean isVegetarian;
        private Boolean isVegan;
        private Boolean isGlutenFree;
        private String restaurantName;
        private String restaurantLogoUrl;
    }
}
