package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * DTO for returning content data in API responses.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Response object containing content information")
public class ContentResponse {
    @Schema(description = "Unique identifier of the content", example = "123")
    private Long id;
    
    @Schema(description = "Title of the content", example = "Delicious Pasta Recipe", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;
    
    @Schema(description = "Detailed description of the content", example = "A step-by-step guide to making delicious pasta")
    private String description;
    
    @Schema(description = "URL to the main media file (video/image)", example = "https://example.com/media/123.mp4")
    private String mediaUrl;
    
    @Schema(description = "URL to the thumbnail image", example = "https://example.com/thumbnails/123.jpg")
    private String thumbnailUrl;
    
    @Schema(description = "Type of the content", example = "RECIPE_VIDEO")
    private ContentType contentType;
    
    @Schema(description = "User who created the content")
    private UserProfileResponse creator;
    
    @Schema(description = "Set of categories this content belongs to", example = "[\"COOKING_CLASS\", \"MEAL_DELIVERY\"]")
    private Set<ServiceCategory> categories;
    
    @Schema(description = "Set of tags for better discoverability", example = "[\"pasta\", \"italian\"]")
    private Set<String> tags;
    
    @Schema(description = "Whether the content is public or private", example = "true")
    private boolean isPublic;
    
    @Schema(description = "Whether the content is promoted/featured", example = "false")
    private boolean isPromoted;
    
    @Schema(description = "Duration of the content in seconds (for videos)", example = "300")
    private Integer duration;
    
    @Schema(description = "Timestamp when the content was created", example = "2023-01-01T12:00:00")
    private LocalDateTime createdAt;
    
    @Schema(description = "Timestamp when the content was last updated", example = "2023-01-02T15:30:00")
    private LocalDateTime updatedAt;
    
    @Schema(description = "Number of times the content has been viewed", example = "150")
    private Long viewCount = 0L;
    
    @Schema(description = "Number of likes the content has received", example = "42")
    private Long likeCount = 0L;
    
    @Schema(description = "Number of comments on the content", example = "15")
    private Long commentCount = 0L;
    
    @Schema(description = "Number of times the content has been shared", example = "8")
    private Long shareCount = 0L;
    
    @Schema(description = "Whether the current user has liked this content", example = "true")
    private Boolean isLiked;

    @Schema(description = "Whether the current user has saved/bookmarked this content", example = "false")
    private Boolean isBookmarked;

    @Schema(description = "URL for sharing the content", example = "https://app.reelsnack.com/share/abc123")
    private String shareUrl;
    
    @Schema(description = "Aspect ratio of the media (width/height)", example = "0.5625")
    private Float aspectRatio;
    
    @Schema(description = "MIME type of the media file", example = "video/mp4")
    private String mimeType;
    
    @Schema(description = "File size in bytes", example = "5242880")
    private Long fileSize;
    
    @Schema(description = "Resolution of the media (width x height)", example = "1920x1080")
    private String resolution;
}
