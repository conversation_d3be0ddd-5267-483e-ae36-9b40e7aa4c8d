package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for content bookmarking.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Content bookmark response")
public class BookmarkResponse {

    @Schema(description = "Bookmark ID", example = "123")
    private Long id;

    @Schema(description = "Content ID that was bookmarked", example = "456")
    private Long contentId;

    @Schema(description = "Content information")
    private ContentSummaryResponse content;

    @Schema(description = "Collection name", example = "Favorites")
    private String bookmarkCollection;

    @Schema(description = "Personal notes", example = "Try this recipe for dinner party")
    private String notes;

    @Schema(description = "When the content was bookmarked", example = "2023-12-01T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime bookmarkedAt;

    /**
     * Simplified content information for bookmark responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Content information in bookmark context")
    public static class ContentSummaryResponse {

        @Schema(description = "Content ID", example = "456")
        private Long id;

        @Schema(description = "Content title", example = "Amazing Pasta Recipe")
        private String title;

        @Schema(description = "Content description", example = "Learn how to make authentic Italian pasta")
        private String description;

        @Schema(description = "Thumbnail URL", example = "https://example.com/thumbnail.jpg")
        private String thumbnailUrl;

        @Schema(description = "Content type", example = "RECIPE_VIDEO")
        private String contentType;

        @Schema(description = "Content creator")
        private UserSummaryResponse creator;

        @Schema(description = "Content duration in seconds", example = "180")
        private Integer duration;

        @Schema(description = "Number of views", example = "1250")
        private Long viewCount;

        @Schema(description = "Number of likes", example = "89")
        private Long likeCount;
    }

    /**
     * Simplified user information for bookmark responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "User information in bookmark context")
    public static class UserSummaryResponse {

        @Schema(description = "User ID", example = "123")
        private Long id;

        @Schema(description = "Username", example = "chef_maria")
        private String username;

        @Schema(description = "User's first name", example = "Maria")
        private String firstName;

        @Schema(description = "User's last name", example = "Rodriguez")
        private String lastName;

        @Schema(description = "User's profile picture URL", example = "https://example.com/profile.jpg")
        private String profilePictureUrl;
    }
}
