package com.reelsnack.reelsnack.dto.request;

import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * Request DTOs for restaurant operations.
 */
public class RestaurantRequest {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateRestaurantRequest {
        
        @NotBlank(message = "Restaurant name is required")
        @Size(max = 100, message = "Restaurant name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotBlank(message = "Address is required")
        @Size(max = 200, message = "Address must not exceed 200 characters")
        private String address;
        
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 50, message = "State must not exceed 50 characters")
        private String state;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
        
        @Size(max = 50, message = "Country must not exceed 50 characters")
        private String country;
        
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        private BigDecimal latitude;
        
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        private BigDecimal longitude;
        
        @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        private String phoneNumber;
        
        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;
        
        @Size(max = 255, message = "Website URL must not exceed 255 characters")
        private String website;
        
        @DecimalMin(value = "0.0", message = "Delivery fee must be non-negative")
        private BigDecimal deliveryFee;
        
        @DecimalMin(value = "0.0", message = "Minimum order must be non-negative")
        private BigDecimal minimumOrder;
        
        @Min(value = 1, message = "Estimated delivery time must be at least 1 minute")
        private Integer estimatedDeliveryTime;
        
        @DecimalMin(value = "0.1", message = "Delivery radius must be at least 0.1 km")
        @DecimalMax(value = "50.0", message = "Delivery radius must not exceed 50 km")
        private BigDecimal deliveryRadius;
        
        private LocalTime openingTime;
        private LocalTime closingTime;
        
        private List<String> cuisineTypes;
        private List<String> specialFeatures;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateRestaurantRequest {
        
        @NotBlank(message = "Restaurant name is required")
        @Size(max = 100, message = "Restaurant name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotBlank(message = "Address is required")
        @Size(max = 200, message = "Address must not exceed 200 characters")
        private String address;
        
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 50, message = "State must not exceed 50 characters")
        private String state;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
        
        @Size(max = 50, message = "Country must not exceed 50 characters")
        private String country;
        
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        private BigDecimal latitude;
        
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        private BigDecimal longitude;
        
        @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        private String phoneNumber;
        
        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;
        
        @Size(max = 255, message = "Website URL must not exceed 255 characters")
        private String website;
        
        @DecimalMin(value = "0.0", message = "Delivery fee must be non-negative")
        private BigDecimal deliveryFee;
        
        @DecimalMin(value = "0.0", message = "Minimum order must be non-negative")
        private BigDecimal minimumOrder;
        
        @Min(value = 1, message = "Estimated delivery time must be at least 1 minute")
        private Integer estimatedDeliveryTime;
        
        @DecimalMin(value = "0.1", message = "Delivery radius must be at least 0.1 km")
        @DecimalMax(value = "50.0", message = "Delivery radius must not exceed 50 km")
        private BigDecimal deliveryRadius;
        
        private LocalTime openingTime;
        private LocalTime closingTime;
        
        private List<String> cuisineTypes;
        private List<String> specialFeatures;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateStatusRequest {
        
        @NotNull(message = "Status is required")
        private RestaurantStatus status;
        
        @Size(max = 1000, message = "Admin notes must not exceed 1000 characters")
        private String adminNotes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestaurantSearchRequest {
        
        private String name;
        private String city;
        private String cuisine;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private BigDecimal maxDeliveryFee;
        private BigDecimal minRating;
        private Integer maxDeliveryTime;
        private Boolean isOpen;
        private Boolean isVerified;
        private Boolean isFeatured;
        
        @Min(value = 0, message = "Page must be non-negative")
        private Integer page = 0;
        
        @Min(value = 1, message = "Size must be at least 1")
        @Max(value = 100, message = "Size must not exceed 100")
        private Integer size = 20;
        
        private String sortBy = "name";
        private String sortDirection = "asc";
    }
}
