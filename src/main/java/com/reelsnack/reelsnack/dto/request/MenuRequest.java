package com.reelsnack.reelsnack.dto.request;

import com.reelsnack.reelsnack.model.enums.OptionType;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Request DTOs for menu operations.
 */
public class MenuRequest {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateCategoryRequest {
        
        @NotBlank(message = "Category name is required")
        @Size(max = 100, message = "Category name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        private Integer sortOrder;
        private Boolean isActive = true;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateCategoryRequest {
        
        @NotBlank(message = "Category name is required")
        @Size(max = 100, message = "Category name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        private Integer sortOrder;
        private Boolean isActive;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateItemRequest {
        
        @NotBlank(message = "Item name is required")
        @Size(max = 100, message = "Item name must not exceed 100 characters")
        private String name;
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @NotNull(message = "Price is required")
        @DecimalMin(value = "0.01", message = "Price must be greater than 0")
        private BigDecimal price;
        
        @DecimalMin(value = "0.0", message = "Original price must be non-negative")
        private BigDecimal originalPrice;
        
        private Boolean isAvailable = true;
        private Boolean isFeatured = false;
        private Boolean isVegetarian = false;
        private Boolean isVegan = false;
        private Boolean isGlutenFree = false;
        private Boolean isSpicy = false;
        
        @Min(value = 0, message = "Spice level must be between 0 and 5")
        @Max(value = 5, message = "Spice level must be between 0 and 5")
        private Integer spiceLevel = 0;
        
        @Min(value = 0, message = "Calories must be non-negative")
        private Integer calories;
        
        @Min(value = 1, message = "Preparation time must be at least 1 minute")
        private Integer preparationTime = 15;
        
        private Integer sortOrder;
        
        private List<String> ingredients;
        private List<String> allergens;
        private String nutritionalInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateItemRequest {
        
        @NotBlank(message = "Item name is required")
        @Size(max = 100, message = "Item name must not exceed 100 characters")
        private String name;
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @NotNull(message = "Price is required")
        @DecimalMin(value = "0.01", message = "Price must be greater than 0")
        private BigDecimal price;
        
        @DecimalMin(value = "0.0", message = "Original price must be non-negative")
        private BigDecimal originalPrice;
        
        private Boolean isAvailable;
        private Boolean isFeatured;
        private Boolean isVegetarian;
        private Boolean isVegan;
        private Boolean isGlutenFree;
        private Boolean isSpicy;
        
        @Min(value = 0, message = "Spice level must be between 0 and 5")
        @Max(value = 5, message = "Spice level must be between 0 and 5")
        private Integer spiceLevel;
        
        @Min(value = 0, message = "Calories must be non-negative")
        private Integer calories;
        
        @Min(value = 1, message = "Preparation time must be at least 1 minute")
        private Integer preparationTime;
        
        private Integer sortOrder;
        
        private List<String> ingredients;
        private List<String> allergens;
        private String nutritionalInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateOptionRequest {
        
        @NotBlank(message = "Option name is required")
        @Size(max = 100, message = "Option name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotNull(message = "Option type is required")
        private OptionType optionType;
        
        private Boolean isRequired = false;
        private Boolean isMultipleChoice = false;
        
        @Min(value = 0, message = "Minimum selections must be non-negative")
        private Integer minSelections = 0;
        
        @Min(value = 1, message = "Maximum selections must be at least 1")
        private Integer maxSelections = 1;
        
        private Integer sortOrder;
        
        @NotEmpty(message = "Option must have at least one value")
        private List<CreateOptionValueRequest> values;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateOptionRequest {
        
        @NotBlank(message = "Option name is required")
        @Size(max = 100, message = "Option name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotNull(message = "Option type is required")
        private OptionType optionType;
        
        private Boolean isRequired;
        private Boolean isMultipleChoice;
        
        @Min(value = 0, message = "Minimum selections must be non-negative")
        private Integer minSelections;
        
        @Min(value = 1, message = "Maximum selections must be at least 1")
        private Integer maxSelections;
        
        private Integer sortOrder;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateOptionValueRequest {
        
        @NotBlank(message = "Option value name is required")
        @Size(max = 100, message = "Option value name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotNull(message = "Additional price is required")
        @DecimalMin(value = "0.0", message = "Additional price must be non-negative")
        private BigDecimal additionalPrice = BigDecimal.ZERO;
        
        private Boolean isAvailable = true;
        private Boolean isDefault = false;
        private Integer sortOrder;
        
        @Min(value = 0, message = "Additional calories must be non-negative")
        private Integer additionalCalories = 0;
        
        @Min(value = 0, message = "Additional prep time must be non-negative")
        private Integer additionalPrepTime = 0;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateOptionValueRequest {
        
        @NotBlank(message = "Option value name is required")
        @Size(max = 100, message = "Option value name must not exceed 100 characters")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        private String description;
        
        @NotNull(message = "Additional price is required")
        @DecimalMin(value = "0.0", message = "Additional price must be non-negative")
        private BigDecimal additionalPrice;
        
        private Boolean isAvailable;
        private Boolean isDefault;
        private Integer sortOrder;
        
        @Min(value = 0, message = "Additional calories must be non-negative")
        private Integer additionalCalories;
        
        @Min(value = 0, message = "Additional prep time must be non-negative")
        private Integer additionalPrepTime;
    }
}
