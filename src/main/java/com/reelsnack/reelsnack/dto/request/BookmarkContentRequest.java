package com.reelsnack.reelsnack.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for bookmarking content.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to bookmark content")
public class BookmarkContentRequest {

    @Schema(description = "Collection name to organize bookmarks", 
           example = "Favorites")
    @Size(max = 100, message = "Bookmark collection name cannot exceed 100 characters")
    private String bookmarkCollection;

    @Schema(description = "Personal notes about the bookmarked content", 
           example = "Try this recipe for dinner party")
    @Size(max = 500, message = "Notes cannot exceed 500 characters")
    private String notes;
}
