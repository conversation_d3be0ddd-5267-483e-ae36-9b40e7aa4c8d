package com.reelsnack.reelsnack.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for sharing content.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to share content")
public class ShareContentRequest {

    @Schema(description = "Platform where content is being shared", 
           example = "FACEBOOK",
           allowableValues = {"FACEBOOK", "TWITTER", "WHATSAPP", "INSTAGRAM", "COPY_LINK", "EMAIL", "SMS"})
    @Size(max = 50, message = "Share platform cannot exceed 50 characters")
    private String sharePlatform;

    @Schema(description = "Optional message to include when sharing", 
           example = "Check out this amazing recipe!")
    @Size(max = 500, message = "Share message cannot exceed 500 characters")
    private String shareMessage;
}
