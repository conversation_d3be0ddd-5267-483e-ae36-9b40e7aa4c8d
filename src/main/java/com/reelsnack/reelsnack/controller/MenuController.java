package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.request.MenuRequest;
import com.reelsnack.reelsnack.dto.response.MenuResponse;
import com.reelsnack.reelsnack.service.MenuManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * REST controller for menu management operations.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/menus")
@RequiredArgsConstructor
@Tag(name = "Menu Management", description = "APIs for managing restaurant menus")
public class MenuController {

    private final MenuManagementService menuManagementService;

    // ===== MENU CATEGORY ENDPOINTS =====

    @Operation(summary = "Create menu category", description = "Create a new menu category for restaurant")
    @PostMapping("/restaurants/{restaurantId}/categories")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.CategoryResponse> createCategory(
            @PathVariable Long restaurantId,
            @Valid @RequestBody MenuRequest.CreateCategoryRequest request,
            Authentication authentication) {
        log.info("Creating menu category for restaurant ID: {}", restaurantId);
        MenuResponse.CategoryResponse response = menuManagementService.createCategory(restaurantId, request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Update menu category", description = "Update menu category details")
    @PutMapping("/categories/{categoryId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.CategoryResponse> updateCategory(
            @PathVariable Long categoryId,
            @Valid @RequestBody MenuRequest.UpdateCategoryRequest request,
            Authentication authentication) {
        log.info("Updating menu category ID: {}", categoryId);
        MenuResponse.CategoryResponse response = menuManagementService.updateCategory(categoryId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get restaurant categories", description = "Get all menu categories for a restaurant")
    @GetMapping("/restaurants/{restaurantId}/categories")
    public ResponseEntity<List<MenuResponse.CategoryResponse>> getRestaurantCategories(
            @PathVariable Long restaurantId,
            @Parameter(description = "Show only active categories") @RequestParam(defaultValue = "true") boolean activeOnly) {
        List<MenuResponse.CategoryResponse> categories = menuManagementService.getRestaurantCategories(restaurantId, activeOnly);
        return ResponseEntity.ok(categories);
    }

    @Operation(summary = "Delete menu category", description = "Delete a menu category")
    @DeleteMapping("/categories/{categoryId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<Void> deleteCategory(
            @PathVariable Long categoryId,
            Authentication authentication) {
        log.info("Deleting menu category ID: {}", categoryId);
        menuManagementService.deleteCategory(categoryId, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    // ===== MENU ITEM ENDPOINTS =====

    @Operation(summary = "Create menu item", description = "Create a new menu item in category")
    @PostMapping("/categories/{categoryId}/items")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.ItemResponse> createMenuItem(
            @PathVariable Long categoryId,
            @Valid @RequestBody MenuRequest.CreateItemRequest request,
            Authentication authentication) {
        log.info("Creating menu item for category ID: {}", categoryId);
        MenuResponse.ItemResponse response = menuManagementService.createMenuItem(categoryId, request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Update menu item", description = "Update menu item details")
    @PutMapping("/items/{itemId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.ItemResponse> updateMenuItem(
            @PathVariable Long itemId,
            @Valid @RequestBody MenuRequest.UpdateItemRequest request,
            Authentication authentication) {
        log.info("Updating menu item ID: {}", itemId);
        MenuResponse.ItemResponse response = menuManagementService.updateMenuItem(itemId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Upload menu item image", description = "Upload image for menu item")
    @PostMapping(value = "/items/{itemId}/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.ItemResponse> uploadItemImage(
            @PathVariable Long itemId,
            @RequestParam("image") MultipartFile imageFile,
            Authentication authentication) {
        log.info("Uploading image for menu item ID: {}", itemId);
        MenuResponse.ItemResponse response = menuManagementService.uploadItemImage(itemId, imageFile, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get category items", description = "Get all menu items in a category")
    @GetMapping("/categories/{categoryId}/items")
    public ResponseEntity<List<MenuResponse.ItemResponse>> getCategoryItems(
            @PathVariable Long categoryId,
            @Parameter(description = "Show only available items") @RequestParam(defaultValue = "true") boolean availableOnly) {
        List<MenuResponse.ItemResponse> items = menuManagementService.getCategoryItems(categoryId, availableOnly);
        return ResponseEntity.ok(items);
    }

    @Operation(summary = "Get restaurant items", description = "Get all menu items for a restaurant with pagination")
    @GetMapping("/restaurants/{restaurantId}/items")
    public ResponseEntity<Page<MenuResponse.ItemResponse>> getRestaurantItems(
            @PathVariable Long restaurantId,
            @Parameter(description = "Show only available items") @RequestParam(defaultValue = "true") boolean availableOnly,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDirection) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<MenuResponse.ItemResponse> items = menuManagementService.getRestaurantItems(restaurantId, availableOnly, pageable);
        return ResponseEntity.ok(items);
    }

    @Operation(summary = "Search menu items", description = "Search menu items by name within restaurant")
    @GetMapping("/restaurants/{restaurantId}/items/search")
    public ResponseEntity<Page<MenuResponse.ItemResponse>> searchMenuItems(
            @PathVariable Long restaurantId,
            @Parameter(description = "Search query") @RequestParam String query,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MenuResponse.ItemResponse> items = menuManagementService.searchMenuItems(restaurantId, query, pageable);
        return ResponseEntity.ok(items);
    }

    @Operation(summary = "Get featured items", description = "Get featured menu items for restaurant")
    @GetMapping("/restaurants/{restaurantId}/items/featured")
    public ResponseEntity<List<MenuResponse.ItemResponse>> getFeaturedItems(@PathVariable Long restaurantId) {
        List<MenuResponse.ItemResponse> items = menuManagementService.getFeaturedItems(restaurantId);
        return ResponseEntity.ok(items);
    }

    @Operation(summary = "Toggle item availability", description = "Toggle menu item availability")
    @PatchMapping("/items/{itemId}/toggle-availability")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<MenuResponse.ItemResponse> toggleItemAvailability(
            @PathVariable Long itemId,
            Authentication authentication) {
        log.info("Toggling availability for menu item ID: {}", itemId);
        MenuResponse.ItemResponse response = menuManagementService.toggleItemAvailability(itemId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete menu item", description = "Delete a menu item")
    @DeleteMapping("/items/{itemId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<Void> deleteMenuItem(
            @PathVariable Long itemId,
            Authentication authentication) {
        log.info("Deleting menu item ID: {}", itemId);
        menuManagementService.deleteMenuItem(itemId, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    // ===== PUBLIC MENU ENDPOINTS =====

    @Operation(summary = "Get restaurant menu", description = "Get complete menu for a restaurant (public endpoint)")
    @GetMapping("/restaurants/{restaurantId}")
    public ResponseEntity<List<MenuResponse.CategoryWithItems>> getRestaurantMenu(@PathVariable Long restaurantId) {
        List<MenuResponse.CategoryResponse> categories = menuManagementService.getRestaurantCategories(restaurantId, true);
        
        // Convert to CategoryWithItems (this would need additional service method)
        // For now, return empty list - this would be implemented in the service
        return ResponseEntity.ok(List.of());
    }

    @Operation(summary = "Get menu item details", description = "Get detailed information about a menu item")
    @GetMapping("/items/{itemId}")
    public ResponseEntity<MenuResponse.ItemResponse> getMenuItem(@PathVariable Long itemId) {
        // This would need a service method to get item by ID
        // For now, return not implemented
        return ResponseEntity.notFound().build();
    }
}
