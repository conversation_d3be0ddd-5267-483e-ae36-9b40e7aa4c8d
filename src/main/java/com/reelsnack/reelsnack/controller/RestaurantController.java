package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.request.RestaurantRequest;
import com.reelsnack.reelsnack.dto.response.RestaurantResponse;
import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import com.reelsnack.reelsnack.service.RestaurantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;

/**
 * REST controller for restaurant management operations.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/restaurants")
@RequiredArgsConstructor
@Tag(name = "Restaurant Management", description = "APIs for managing restaurants")
public class RestaurantController {

    private final RestaurantService restaurantService;

    @Operation(summary = "Create a new restaurant", description = "Create a new restaurant for the authenticated user")
    @PostMapping
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> createRestaurant(
            @Valid @RequestBody RestaurantRequest.CreateRestaurantRequest request,
            Authentication authentication) {
        log.info("Creating restaurant for user: {}", authentication.getName());
        RestaurantResponse response = restaurantService.createRestaurant(request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Update restaurant information", description = "Update restaurant details")
    @PutMapping("/{restaurantId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> updateRestaurant(
            @PathVariable Long restaurantId,
            @Valid @RequestBody RestaurantRequest.UpdateRestaurantRequest request,
            Authentication authentication) {
        log.info("Updating restaurant ID: {} by user: {}", restaurantId, authentication.getName());
        RestaurantResponse response = restaurantService.updateRestaurant(restaurantId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Upload restaurant logo", description = "Upload logo image for restaurant")
    @PostMapping(value = "/{restaurantId}/logo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> uploadLogo(
            @PathVariable Long restaurantId,
            @RequestParam("logo") MultipartFile logoFile,
            Authentication authentication) {
        log.info("Uploading logo for restaurant ID: {}", restaurantId);
        RestaurantResponse response = restaurantService.uploadLogo(restaurantId, logoFile, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Upload restaurant cover image", description = "Upload cover image for restaurant")
    @PostMapping(value = "/{restaurantId}/cover", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> uploadCoverImage(
            @PathVariable Long restaurantId,
            @RequestParam("cover") MultipartFile coverFile,
            Authentication authentication) {
        log.info("Uploading cover image for restaurant ID: {}", restaurantId);
        RestaurantResponse response = restaurantService.uploadCoverImage(restaurantId, coverFile, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get restaurant by ID", description = "Retrieve restaurant details by ID")
    @GetMapping("/{restaurantId}")
    public ResponseEntity<RestaurantResponse> getRestaurant(@PathVariable Long restaurantId) {
        RestaurantResponse response = restaurantService.getRestaurant(restaurantId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get my restaurant", description = "Get restaurant owned by authenticated user")
    @GetMapping("/my-restaurant")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> getMyRestaurant(Authentication authentication) {
        RestaurantResponse response = restaurantService.getRestaurantByOwner(authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get all restaurants", description = "Get paginated list of restaurants with filters")
    @GetMapping
    public ResponseEntity<Page<RestaurantResponse>> getRestaurants(
            @Parameter(description = "Restaurant status filter") @RequestParam(required = false) RestaurantStatus status,
            @Parameter(description = "Filter by open status") @RequestParam(required = false) Boolean isOpen,
            @Parameter(description = "Filter by verified status") @RequestParam(required = false) Boolean isVerified,
            @Parameter(description = "Filter by featured status") @RequestParam(required = false) Boolean isFeatured,
            @Parameter(description = "Filter by city") @RequestParam(required = false) String city,
            @Parameter(description = "Minimum rating filter") @RequestParam(required = false) BigDecimal minRating,
            @Parameter(description = "Maximum delivery fee filter") @RequestParam(required = false) BigDecimal maxDeliveryFee,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDirection) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDirection), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<RestaurantResponse> restaurants = restaurantService.getRestaurants(
            status, isOpen, isVerified, isFeatured, city, minRating, maxDeliveryFee, pageable);
        
        return ResponseEntity.ok(restaurants);
    }

    @Operation(summary = "Search restaurants", description = "Search restaurants by name")
    @GetMapping("/search")
    public ResponseEntity<Page<RestaurantResponse>> searchRestaurants(
            @Parameter(description = "Search query") @RequestParam String query,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RestaurantResponse> restaurants = restaurantService.searchRestaurants(query, pageable);
        return ResponseEntity.ok(restaurants);
    }

    @Operation(summary = "Find nearby restaurants", description = "Find restaurants within delivery radius of given coordinates")
    @GetMapping("/nearby")
    public ResponseEntity<Page<RestaurantResponse>> findNearbyRestaurants(
            @Parameter(description = "Latitude") @RequestParam BigDecimal latitude,
            @Parameter(description = "Longitude") @RequestParam BigDecimal longitude,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RestaurantResponse> restaurants = restaurantService.findRestaurantsNearby(latitude, longitude, pageable);
        return ResponseEntity.ok(restaurants);
    }

    @Operation(summary = "Get top-rated restaurants", description = "Get restaurants with highest ratings")
    @GetMapping("/top-rated")
    public ResponseEntity<Page<RestaurantResponse>> getTopRatedRestaurants(
            @Parameter(description = "Minimum number of reviews") @RequestParam(defaultValue = "5") Integer minReviews,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RestaurantResponse> restaurants = restaurantService.getTopRatedRestaurants(minReviews, pageable);
        return ResponseEntity.ok(restaurants);
    }

    @Operation(summary = "Get featured restaurants", description = "Get featured restaurants")
    @GetMapping("/featured")
    public ResponseEntity<Page<RestaurantResponse>> getFeaturedRestaurants(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RestaurantResponse> restaurants = restaurantService.getFeaturedRestaurants(pageable);
        return ResponseEntity.ok(restaurants);
    }

    @Operation(summary = "Toggle restaurant open/closed status", description = "Toggle restaurant availability")
    @PatchMapping("/{restaurantId}/toggle-status")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<RestaurantResponse> toggleRestaurantStatus(
            @PathVariable Long restaurantId,
            Authentication authentication) {
        log.info("Toggling status for restaurant ID: {}", restaurantId);
        RestaurantResponse response = restaurantService.toggleRestaurantStatus(restaurantId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update restaurant status (Admin)", description = "Update restaurant status - Admin only")
    @PatchMapping("/{restaurantId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<RestaurantResponse> updateRestaurantStatus(
            @PathVariable Long restaurantId,
            @Valid @RequestBody RestaurantRequest.UpdateStatusRequest request) {
        log.info("Admin updating restaurant ID: {} status to: {}", restaurantId, request.getStatus());
        RestaurantResponse response = restaurantService.updateRestaurantStatus(
            restaurantId, request.getStatus(), request.getAdminNotes());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete restaurant", description = "Soft delete restaurant (set status to INACTIVE)")
    @DeleteMapping("/{restaurantId}")
    @PreAuthorize("hasRole('RESTAURANT') or hasRole('CHEF')")
    public ResponseEntity<Void> deleteRestaurant(
            @PathVariable Long restaurantId,
            Authentication authentication) {
        log.info("Deleting restaurant ID: {}", restaurantId);
        restaurantService.deleteRestaurant(restaurantId, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    // ===== CHEF MANAGEMENT ENDPOINTS =====

    @Operation(summary = "Add chef to restaurant", description = "Associate a chef with the restaurant")
    @PostMapping("/{restaurantId}/chefs/{chefId}")
    @PreAuthorize("hasRole('RESTAURANT')")
    public ResponseEntity<Void> addChefToRestaurant(
            @PathVariable Long restaurantId,
            @PathVariable Long chefId,
            Authentication authentication) {
        log.info("Adding chef ID: {} to restaurant ID: {}", chefId, restaurantId);
        restaurantService.addChefToRestaurant(restaurantId, chefId, authentication.getName());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "Remove chef from restaurant", description = "Remove chef association from restaurant")
    @DeleteMapping("/{restaurantId}/chefs/{chefId}")
    @PreAuthorize("hasRole('RESTAURANT')")
    public ResponseEntity<Void> removeChefFromRestaurant(
            @PathVariable Long restaurantId,
            @PathVariable Long chefId,
            Authentication authentication) {
        log.info("Removing chef ID: {} from restaurant ID: {}", chefId, restaurantId);
        restaurantService.removeChefFromRestaurant(restaurantId, chefId, authentication.getName());
        return ResponseEntity.noContent().build();
    }
}
