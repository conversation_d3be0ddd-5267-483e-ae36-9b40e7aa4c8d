package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.request.CreateContentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateContentRequest;
import com.reelsnack.reelsnack.dto.response.ApiResponse;
import com.reelsnack.reelsnack.dto.response.ContentResponse;
import com.reelsnack.reelsnack.exception.ErrorResponse;
import com.reelsnack.reelsnack.exception.UnsupportedMediaTypeException;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.ContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.http.MediaType;
import org.springframework.web.server.UnsupportedMediaTypeStatusException;

import java.io.IOException;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/content")
@RequiredArgsConstructor
@Tag(name = "Content", description = "APIs for managing content (videos, tutorials, recipes, etc.)")
public class ContentController {

    private final ContentService contentService;
    private final ContentSocialService contentSocialService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @SecurityRequirement(name = "bearerAuth")
    @Operation(
        summary = "Create new content",
        description = "Upload new content (video, tutorial, recipe, etc.)"
    )
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "201", 
                description = "Content created successfully",
                content = @Content(schema = @Schema(implementation = ContentResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "400", 
                description = "Invalid input or file type",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "401", 
                description = "Authentication required",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "403", 
                description = "Insufficient permissions",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "413", 
                description = "File size too large",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "415", 
                description = "Unsupported media type",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                responseCode = "500", 
                description = "Internal server error",
                content = @Content(schema = @Schema(implementation = ErrorResponse.class))
            )
    })
    @PreAuthorize("hasAnyRole('ROLE_USER', 'ROLE_CHEF', 'ROLE_TUTOR', 'ROLE_RESTAURANT')")
    public ResponseEntity<?> createContent(
            @Parameter(
                description = "Content metadata", 
                required = true,
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateContentRequest.class)
                )
            )
            @RequestPart("request") @Valid CreateContentRequest request,
            
            @Parameter(
                description = "Media file (video/image)", 
                required = true,
                content = @Content(
                    mediaType = "application/octet-stream",
                    schema = @Schema(type = "string", format = "binary")
                )
            )
            @RequestPart("file") @NotNull(message = "File is required") MultipartFile file,
            
            @Parameter(hidden = true)
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        if (userDetails == null) {
            throw new AccessDeniedException("Authentication required");
        }
        
        log.info("Received content creation request from user: {}", userDetails.getUsername());
        
        try {
            ContentResponse response = contentService.createContent(request, file, userDetails);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (UnsupportedMediaTypeException e) {
            log.warn("Unsupported media type: {}", e.getMessage());
            return ResponseEntity
                    .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
                    .body(new ErrorResponse("Unsupported media type", e.getMessage()));
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request: {}", e.getMessage());
            return ResponseEntity
                    .badRequest()
                    .body(new ErrorResponse("Invalid request", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating content: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Error creating content", e.getMessage()));
        }
    }

    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Update content", 
              description = "Update existing content (metadata and/or media file)")
    @PreAuthorize("hasAnyRole('ROLE_USER', 'ROLE_CHEF', 'ROLE_TUTOR', 'ROLE_RESTAURANT')")
    public ResponseEntity<ContentResponse> updateContent(
            @PathVariable @Positive(message = "ID must be positive") Long id,
            @Valid @RequestPart(required = false) UpdateContentRequest request,
            @RequestPart(value = "file", required = false) MultipartFile file,
            @AuthenticationPrincipal UserDetailsImpl userDetails) throws IOException {
        
        ContentResponse response = contentService.updateContent(id, request, file, userDetails);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get content feed",
              description = "Get a paginated feed of public content with optional filtering")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved content feed"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid pagination parameters")
    })
    public ResponseEntity<Page<ContentResponse>> getContentFeed(
            @Parameter(description = "Filter by content type")
            @RequestParam(required = false) ContentType type,
            
            @Parameter(description = "Filter by category")
            @RequestParam(required = false) String category,
            
            @Parameter(description = "Filter by tags (comma-separated)")
            @RequestParam(required = false) List<String> tags,
            
            @Parameter(description = "Pagination parameters")
            @PageableDefault(size = 20) Pageable pageable) {
        
        // Delegate to appropriate service method based on filters
        Page<ContentResponse> response;
        if (type != null && category != null) {
            response = contentService.getContentByTypeAndCategory(type, category, pageable);
        } else if (type != null) {
            response = contentService.getContentByType(type, pageable);
        } else if (category != null) {
            response = contentService.getContentByCategory(category, pageable);
        } else if (tags != null && !tags.isEmpty()) {
            response = contentService.getContentByTags(tags, pageable);
        } else {
            response = contentService.getContentFeed(pageable);
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get content by ID",
              description = "Get content details by its ID")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Content found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Content not found")
    })
    public ResponseEntity<ContentResponse> getContentById(
            @Parameter(description = "Content ID", required = true)
            @PathVariable @Positive(message = "ID must be positive") Long id) {
        
        ContentResponse response = contentService.getContentById(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/user/{username}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get content by user",
              description = "Get paginated content uploaded by a specific user")
    public ResponseEntity<Page<ContentResponse>> getContentByUser(
            @Parameter(description = "Username of the content creator", required = true)
            @PathVariable @NotBlank(message = "Username is required") String username,
            
            @Parameter(description = "Filter by content type")
            @RequestParam(required = false) ContentType type,
            
            @Parameter(description = "Pagination parameters")
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ContentResponse> response;
        if (type != null) {
            response = contentService.getContentByTypeAndUser(type, username, pageable);
        } else {
            response = contentService.getContentByUser(username, pageable);
        }
        return ResponseEntity.ok(response);
    }

    @GetMapping("/search")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Search content",
              description = "Search content by title, description, or tags with optional content type filtering")
    public ResponseEntity<Page<ContentResponse>> searchContent(
            @Parameter(description = "Search query", required = true)
            @RequestParam @NotBlank(message = "Search query is required") String query,
            
            @Parameter(description = "Filter by content type")
            @RequestParam(required = false) ContentType type,
            
            @Parameter(description = "Pagination parameters")
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ContentResponse> response = (type != null) 
            ? contentService.searchContentByType(query, type, pageable)
            : contentService.searchContent(query, pageable);
            
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete content", 
              description = "Delete content by ID (only owner or admin can delete)")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Content deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Not authorized to delete this content"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Content not found")
    })
    @PreAuthorize("hasAnyRole('ROLE_USER', 'ROLE_CHEF', 'ROLE_TUTOR', 'ROLE_RESTAURANT', 'ROLE_ADMIN')")
    public ResponseEntity<com.reelsnack.reelsnack.dto.response.ApiResponse> deleteContent(
            @Parameter(description = "Content ID to delete", required = true)
            @PathVariable @Positive(message = "ID must be positive") Long id,
            
            @Parameter(hidden = true)
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        contentService.deleteContent(id, userDetails);
        return ResponseEntity.ok(com.reelsnack.reelsnack.dto.response.ApiResponse.success("Content deleted successfully"));
    }
    
    @PostMapping("/{id}/like")
    @Operation(summary = "Like or unlike content")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<String>> toggleLike(
            @PathVariable @Positive Long id,
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        boolean liked = contentService.toggleLike(id, userDetails);
        String message = liked ? "Content liked successfully" : "Content unliked successfully";
        return ResponseEntity.ok(ApiResponse.success(message, null));
    }
    
    @GetMapping("/{id}/likes/count")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get like count for content")
    public ResponseEntity<Long> getLikeCount(@PathVariable @Positive Long id) {
        long count = contentService.getLikeCount(id);
        return ResponseEntity.ok(count);
    }
    
    @GetMapping("/trending")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get trending content",
              description = "Get a list of currently trending content based on engagement")
    public ResponseEntity<Page<ContentResponse>> getTrendingContent(
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,

            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page) {

        // Create pageable without sorting since the native query has its own ORDER BY
        Pageable pageable = PageRequest.of(page, size);

        Page<ContentResponse> trending = contentService.getTrendingContent(pageable);
        return ResponseEntity.ok(trending);
    }

    // ===== Content Categories Endpoints =====

    @GetMapping("/categories")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get all content categories",
              description = "Get a list of all available content categories")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved categories")
    })
    public ResponseEntity<List<String>> getContentCategories() {
        List<String> categories = contentService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/category/{category}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get content by category",
              description = "Get paginated content filtered by a specific category")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved content by category"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid category")
    })
    public ResponseEntity<Page<ContentResponse>> getContentByCategory(
            @Parameter(description = "Content category", required = true, example = "RECIPE_VIDEO")
            @PathVariable String category,

            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,

            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,

            @Parameter(description = "Sort by field", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String sortBy,

            @Parameter(description = "Sort direction", example = "desc")
            @RequestParam(defaultValue = "desc") String sortDir,

            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<ContentResponse> content = contentService.getContentByCategory(category, pageable);

        return ResponseEntity.ok(content);
    }
}
