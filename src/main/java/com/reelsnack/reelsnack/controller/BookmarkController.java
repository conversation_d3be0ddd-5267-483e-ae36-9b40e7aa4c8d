package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.response.BookmarkResponse;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.ContentSocialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing user bookmarks.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/bookmarks")
@RequiredArgsConstructor
@Tag(name = "Bookmarks", description = "User bookmark management")
public class BookmarkController {

    private final ContentSocialService contentSocialService;

    @GetMapping
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get user's bookmarks", 
              description = "Get a paginated list of the current user's bookmarked content")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved bookmarks"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Page<BookmarkResponse>> getUserBookmarks(
            @Parameter(description = "Collection name to filter by", example = "Favorites")
            @RequestParam(required = false) String collection,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size);
        Page<BookmarkResponse> bookmarks = contentSocialService.getUserBookmarks(
            userDetails.getUsername(), collection, pageable);
        
        return ResponseEntity.ok(bookmarks);
    }

    @GetMapping("/collections")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get user's bookmark collections", 
              description = "Get a list of all bookmark collection names for the current user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved collections"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<List<String>> getUserBookmarkCollections(
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        List<String> collections = contentSocialService.getUserBookmarkCollections(userDetails.getUsername());
        return ResponseEntity.ok(collections);
    }

    @GetMapping("/search")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Search user's bookmarks", 
              description = "Search through the current user's bookmarks by content title or notes")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully searched bookmarks"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Page<BookmarkResponse>> searchUserBookmarks(
            @Parameter(description = "Search term", required = true, example = "pasta recipe")
            @RequestParam String q,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size);
        Page<BookmarkResponse> bookmarks = contentSocialService.searchUserBookmarks(
            userDetails.getUsername(), q, pageable);
        
        return ResponseEntity.ok(bookmarks);
    }

    @GetMapping("/user/{username}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get public bookmarks by user", 
              description = "Get a paginated list of public bookmarks by a specific user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved public bookmarks"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<Page<BookmarkResponse>> getPublicUserBookmarks(
            @Parameter(description = "Username of the user", required = true, example = "chef_maria")
            @PathVariable String username,
            
            @Parameter(description = "Collection name to filter by", example = "Favorites")
            @RequestParam(required = false) String collection,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<BookmarkResponse> bookmarks = contentSocialService.getUserBookmarks(username, collection, pageable);
        
        return ResponseEntity.ok(bookmarks);
    }
}
