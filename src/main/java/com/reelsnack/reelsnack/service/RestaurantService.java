package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.RestaurantRequest;
import com.reelsnack.reelsnack.dto.response.RestaurantResponse;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.UnauthorizedException;
import com.reelsnack.reelsnack.mapper.RestaurantMapper;
import com.reelsnack.reelsnack.model.Restaurant;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.RestaurantStatus;
import com.reelsnack.reelsnack.repository.RestaurantRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * Service class for managing restaurant operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class RestaurantService {

    private final RestaurantRepository restaurantRepository;
    private final UserService userService;
    private final FileStorageService fileStorageService;
    private final ThumbnailGenerationService thumbnailGenerationService;
    private final RestaurantMapper restaurantMapper;

    /**
     * Create a new restaurant
     */
    @Transactional
    public RestaurantResponse createRestaurant(RestaurantRequest.CreateRestaurantRequest request, String username) {
        log.info("Creating restaurant for user: {}", username);
        
        User owner = userService.findByUsername(username);
        
        // Check if user already has a restaurant
        if (restaurantRepository.findByOwner(owner).isPresent()) {
            throw new IllegalStateException("User already has a restaurant");
        }
        
        // Check if restaurant name already exists
        if (restaurantRepository.existsByNameIgnoreCase(request.getName())) {
            throw new IllegalArgumentException("Restaurant name already exists");
        }
        
        Restaurant restaurant = restaurantMapper.toEntity(request);
        restaurant.setOwner(owner);
        restaurant.setStatus(RestaurantStatus.PENDING);
        
        Restaurant savedRestaurant = restaurantRepository.save(restaurant);
        log.info("Created restaurant with ID: {} for user: {}", savedRestaurant.getId(), username);
        
        return restaurantMapper.toResponse(savedRestaurant);
    }

    /**
     * Update restaurant information
     */
    @Transactional
    public RestaurantResponse updateRestaurant(Long restaurantId, RestaurantRequest.UpdateRestaurantRequest request, String username) {
        log.info("Updating restaurant ID: {} by user: {}", restaurantId, username);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        
        // Check name uniqueness if name is being changed
        if (!restaurant.getName().equalsIgnoreCase(request.getName()) &&
            restaurantRepository.existsByNameIgnoreCaseAndIdNot(request.getName(), restaurantId)) {
            throw new IllegalArgumentException("Restaurant name already exists");
        }
        
        restaurantMapper.updateEntityFromRequest(restaurant, request);
        Restaurant updatedRestaurant = restaurantRepository.save(restaurant);
        
        log.info("Updated restaurant ID: {}", restaurantId);
        return restaurantMapper.toResponse(updatedRestaurant);
    }

    /**
     * Upload restaurant logo
     */
    @Transactional
    public RestaurantResponse uploadLogo(Long restaurantId, MultipartFile logoFile, String username) {
        log.info("Uploading logo for restaurant ID: {}", restaurantId);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        
        try {
            String logoUrl = fileStorageService.storeFile(logoFile, "restaurants/logos");
            restaurant.setLogoUrl(logoUrl);
            
            Restaurant updatedRestaurant = restaurantRepository.save(restaurant);
            log.info("Updated logo for restaurant ID: {}", restaurantId);
            
            return restaurantMapper.toResponse(updatedRestaurant);
        } catch (Exception e) {
            log.error("Error uploading logo for restaurant ID: {}", restaurantId, e);
            throw new RuntimeException("Failed to upload logo: " + e.getMessage());
        }
    }

    /**
     * Upload restaurant cover image
     */
    @Transactional
    public RestaurantResponse uploadCoverImage(Long restaurantId, MultipartFile coverFile, String username) {
        log.info("Uploading cover image for restaurant ID: {}", restaurantId);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        
        try {
            String coverUrl = fileStorageService.storeFile(coverFile, "restaurants/covers");
            restaurant.setCoverImageUrl(coverUrl);
            
            Restaurant updatedRestaurant = restaurantRepository.save(restaurant);
            log.info("Updated cover image for restaurant ID: {}", restaurantId);
            
            return restaurantMapper.toResponse(updatedRestaurant);
        } catch (Exception e) {
            log.error("Error uploading cover image for restaurant ID: {}", restaurantId, e);
            throw new RuntimeException("Failed to upload cover image: " + e.getMessage());
        }
    }

    /**
     * Get restaurant by ID
     */
    public RestaurantResponse getRestaurant(Long restaurantId) {
        Restaurant restaurant = getRestaurantById(restaurantId);
        return restaurantMapper.toResponse(restaurant);
    }

    /**
     * Get restaurant by owner username
     */
    public RestaurantResponse getRestaurantByOwner(String username) {
        User owner = userService.findByUsername(username);
        Restaurant restaurant = restaurantRepository.findByOwner(owner)
            .orElseThrow(() -> new ResourceNotFoundException("Restaurant not found for user: " + username));
        return restaurantMapper.toResponse(restaurant);
    }

    /**
     * Get all restaurants with pagination and filters
     */
    public Page<RestaurantResponse> getRestaurants(RestaurantStatus status, Boolean isOpen, Boolean isVerified, 
                                                  Boolean isFeatured, String city, BigDecimal minRating, 
                                                  BigDecimal maxDeliveryFee, Pageable pageable) {
        Page<Restaurant> restaurants = restaurantRepository.findByFilters(
            status, isOpen, isVerified, isFeatured, city, minRating, maxDeliveryFee, pageable);
        return restaurants.map(restaurantMapper::toResponse);
    }

    /**
     * Search restaurants by name
     */
    public Page<RestaurantResponse> searchRestaurants(String name, Pageable pageable) {
        Page<Restaurant> restaurants = restaurantRepository.findByNameContainingIgnoreCaseAndStatus(
            name, RestaurantStatus.ACTIVE, pageable);
        return restaurants.map(restaurantMapper::toResponse);
    }

    /**
     * Find restaurants within delivery radius
     */
    public Page<RestaurantResponse> findRestaurantsNearby(BigDecimal latitude, BigDecimal longitude, Pageable pageable) {
        Page<Restaurant> restaurants = restaurantRepository.findRestaurantsWithinDeliveryRadius(
            latitude, longitude, RestaurantStatus.ACTIVE, pageable);
        return restaurants.map(restaurantMapper::toResponse);
    }

    /**
     * Get top-rated restaurants
     */
    public Page<RestaurantResponse> getTopRatedRestaurants(Integer minReviews, Pageable pageable) {
        Page<Restaurant> restaurants = restaurantRepository.findTopRatedRestaurants(
            RestaurantStatus.ACTIVE, minReviews, pageable);
        return restaurants.map(restaurantMapper::toResponse);
    }

    /**
     * Get featured restaurants
     */
    public Page<RestaurantResponse> getFeaturedRestaurants(Pageable pageable) {
        Page<Restaurant> restaurants = restaurantRepository.findByIsFeaturedTrueAndStatus(
            RestaurantStatus.ACTIVE, pageable);
        return restaurants.map(restaurantMapper::toResponse);
    }

    /**
     * Update restaurant status (Admin only)
     */
    @Transactional
    public RestaurantResponse updateRestaurantStatus(Long restaurantId, RestaurantStatus status, String adminNotes) {
        log.info("Updating restaurant ID: {} status to: {}", restaurantId, status);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        restaurant.setStatus(status);
        if (adminNotes != null) {
            restaurant.setAdminNotes(adminNotes);
        }
        
        Restaurant updatedRestaurant = restaurantRepository.save(restaurant);
        log.info("Updated restaurant ID: {} status to: {}", restaurantId, status);
        
        return restaurantMapper.toResponse(updatedRestaurant);
    }

    /**
     * Toggle restaurant open/closed status
     */
    @Transactional
    public RestaurantResponse toggleRestaurantStatus(Long restaurantId, String username) {
        log.info("Toggling restaurant ID: {} status", restaurantId);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        
        restaurant.setIsOpen(!restaurant.getIsOpen());
        Restaurant updatedRestaurant = restaurantRepository.save(restaurant);
        
        log.info("Toggled restaurant ID: {} status to: {}", restaurantId, restaurant.getIsOpen() ? "OPEN" : "CLOSED");
        return restaurantMapper.toResponse(updatedRestaurant);
    }

    /**
     * Update restaurant rating
     */
    @Transactional
    public void updateRestaurantRating(Long restaurantId, BigDecimal newRating) {
        Restaurant restaurant = getRestaurantById(restaurantId);
        restaurant.updateRating(newRating);
        restaurantRepository.save(restaurant);
        log.info("Updated rating for restaurant ID: {} to: {}", restaurantId, restaurant.getAverageRating());
    }

    /**
     * Delete restaurant (soft delete by setting status to INACTIVE)
     */
    @Transactional
    public void deleteRestaurant(Long restaurantId, String username) {
        log.info("Deleting restaurant ID: {}", restaurantId);
        
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        
        restaurant.setStatus(RestaurantStatus.INACTIVE);
        restaurantRepository.save(restaurant);
        
        log.info("Deleted restaurant ID: {}", restaurantId);
    }

    // Helper methods

    private Restaurant getRestaurantById(Long restaurantId) {
        return restaurantRepository.findById(restaurantId)
            .orElseThrow(() -> new ResourceNotFoundException("Restaurant not found with ID: " + restaurantId));
    }

    private void validateRestaurantOwnership(Restaurant restaurant, String username) {
        if (!restaurant.getOwner().getUsername().equals(username)) {
            throw new UnauthorizedException("You are not authorized to modify this restaurant");
        }
    }
}
