package com.reelsnack.reelsnack.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for managing fallback thumbnails when thumbnail generation fails.
 * Creates and stores default thumbnails for different content types.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FallbackThumbnailService {

    private final FileStorageService fileStorageService;
    private final Map<String, String> fallbackThumbnailUrls = new HashMap<>();

    // Thumbnail dimensions
    private static final int THUMBNAIL_WIDTH = 320;
    private static final int THUMBNAIL_HEIGHT = 240;

    // Color scheme for different content types
    private static final Map<String, Color> CONTENT_TYPE_COLORS = Map.of(
        "RECIPE_VIDEO", new Color(255, 107, 107),      // Red for recipes
        "COOKING_TUTORIAL", new Color(255, 159, 67),   // Orange for tutorials
        "RESTAURANT_PROMO", new Color(72, 219, 251),   // Blue for restaurants
        "TUTORIAL", new Color(162, 155, 254),          // Purple for general tutorials
        "REVIEW", new Color(255, 218, 121),            // Yellow for reviews
        "DEFAULT", new Color(158, 158, 158)            // Gray for default
    );

    // Icons/text for different content types
    private static final Map<String, String> CONTENT_TYPE_LABELS = Map.of(
        "RECIPE_VIDEO", "🍳 Recipe",
        "COOKING_TUTORIAL", "👨‍🍳 Tutorial",
        "RESTAURANT_PROMO", "🏪 Restaurant",
        "TUTORIAL", "📚 Tutorial",
        "REVIEW", "⭐ Review",
        "DEFAULT", "📄 Content"
    );

    /**
     * Initialize fallback thumbnails on service startup.
     */
    @PostConstruct
    public void initializeFallbackThumbnails() {
        log.info("Initializing fallback thumbnails...");
        
        try {
            for (String contentType : CONTENT_TYPE_COLORS.keySet()) {
                String thumbnailUrl = createAndStoreFallbackThumbnail(contentType);
                fallbackThumbnailUrls.put(contentType, thumbnailUrl);
                log.debug("Created fallback thumbnail for {}: {}", contentType, thumbnailUrl);
            }
            log.info("Successfully initialized {} fallback thumbnails", fallbackThumbnailUrls.size());
        } catch (Exception e) {
            log.error("Failed to initialize fallback thumbnails: {}", e.getMessage(), e);
        }
    }

    /**
     * Get fallback thumbnail URL for a content type.
     * 
     * @param contentType The content type
     * @return URL of the fallback thumbnail
     */
    public String getFallbackThumbnailUrl(String contentType) {
        String normalizedType = normalizeContentType(contentType);
        String url = fallbackThumbnailUrls.get(normalizedType);
        
        if (url == null) {
            // If specific type not found, use default
            url = fallbackThumbnailUrls.get("DEFAULT");
        }
        
        if (url == null) {
            // Ultimate fallback - create on demand
            log.warn("No fallback thumbnail found for {}, creating on demand", contentType);
            try {
                url = createAndStoreFallbackThumbnail(normalizedType);
                fallbackThumbnailUrls.put(normalizedType, url);
            } catch (Exception e) {
                log.error("Failed to create fallback thumbnail on demand: {}", e.getMessage(), e);
                return "thumbnails/default.jpg"; // Static fallback
            }
        }
        
        return url;
    }

    /**
     * Create and store a fallback thumbnail for a content type.
     */
    private String createAndStoreFallbackThumbnail(String contentType) throws IOException {
        BufferedImage thumbnail = createFallbackThumbnailImage(contentType);
        
        // Convert to byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(thumbnail, "jpg", outputStream);
        byte[] thumbnailBytes = outputStream.toByteArray();
        
        // Create MultipartFile wrapper
        com.reelsnack.reelsnack.util.ByteArrayMultipartFile thumbnailFile = 
            new com.reelsnack.reelsnack.util.ByteArrayMultipartFile(
                "fallback-thumbnail",
                "fallback-" + contentType.toLowerCase() + ".jpg",
                "image/jpeg",
                thumbnailBytes
            );
        
        // Store the thumbnail
        return fileStorageService.storeFile(thumbnailFile, "thumbnails/fallbacks");
    }

    /**
     * Create a fallback thumbnail image with appropriate styling.
     */
    private BufferedImage createFallbackThumbnailImage(String contentType) {
        BufferedImage image = new BufferedImage(THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        try {
            // Enable anti-aliasing for better quality
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // Get colors and label for this content type
            Color backgroundColor = CONTENT_TYPE_COLORS.getOrDefault(contentType, CONTENT_TYPE_COLORS.get("DEFAULT"));
            String label = CONTENT_TYPE_LABELS.getOrDefault(contentType, CONTENT_TYPE_LABELS.get("DEFAULT"));
            
            // Create gradient background
            GradientPaint gradient = new GradientPaint(
                0, 0, backgroundColor,
                THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, backgroundColor.darker()
            );
            g2d.setPaint(gradient);
            g2d.fillRect(0, 0, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT);
            
            // Add border
            g2d.setColor(backgroundColor.darker().darker());
            g2d.setStroke(new BasicStroke(2));
            g2d.drawRect(1, 1, THUMBNAIL_WIDTH - 2, THUMBNAIL_HEIGHT - 2);
            
            // Add text
            g2d.setColor(Color.WHITE);
            Font font = new Font("Arial", Font.BOLD, 18);
            g2d.setFont(font);
            
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(label);
            int textHeight = fm.getHeight();
            
            int x = (THUMBNAIL_WIDTH - textWidth) / 2;
            int y = (THUMBNAIL_HEIGHT + textHeight) / 2 - fm.getDescent();
            
            // Add text shadow
            g2d.setColor(new Color(0, 0, 0, 100));
            g2d.drawString(label, x + 1, y + 1);
            
            // Add main text
            g2d.setColor(Color.WHITE);
            g2d.drawString(label, x, y);
            
            // Add "ReelSnack" watermark
            Font watermarkFont = new Font("Arial", Font.PLAIN, 10);
            g2d.setFont(watermarkFont);
            g2d.setColor(new Color(255, 255, 255, 150));
            g2d.drawString("ReelSnack", 10, THUMBNAIL_HEIGHT - 10);
            
        } finally {
            g2d.dispose();
        }
        
        return image;
    }

    /**
     * Normalize content type for consistent lookup.
     */
    private String normalizeContentType(String contentType) {
        if (contentType == null) {
            return "DEFAULT";
        }
        
        String normalized = contentType.toUpperCase();
        
        // Map similar types to standard ones
        switch (normalized) {
            case "RECIPE":
            case "RECIPE_VIDEO":
            case "COOKING_VIDEO":
                return "RECIPE_VIDEO";
            case "COOKING_TUTORIAL":
            case "TUTORIAL":
                return "TUTORIAL";
            case "RESTAURANT":
            case "RESTAURANT_PROMO":
            case "RESTAURANT_VIDEO":
                return "RESTAURANT_PROMO";
            case "REVIEW":
            case "FOOD_REVIEW":
                return "REVIEW";
            default:
                return "DEFAULT";
        }
    }

    /**
     * Check if fallback thumbnails are initialized.
     */
    public boolean areFallbackThumbnailsInitialized() {
        return !fallbackThumbnailUrls.isEmpty();
    }

    /**
     * Get count of initialized fallback thumbnails.
     */
    public int getFallbackThumbnailCount() {
        return fallbackThumbnailUrls.size();
    }

    /**
     * Reinitialize fallback thumbnails (useful for testing or updates).
     */
    public void reinitializeFallbackThumbnails() {
        fallbackThumbnailUrls.clear();
        initializeFallbackThumbnails();
    }
}
