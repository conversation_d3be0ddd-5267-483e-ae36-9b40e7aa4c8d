package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.config.properties.ContentProperties;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.info.MultimediaInfo;
import ws.schild.jave.info.VideoSize;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class VideoProcessingService {

    private final FileStorageService fileStorageService;
    private final ContentProperties contentProperties;
    private final ThumbnailGenerationService thumbnailGenerationService;

    /**
     * Generate a thumbnail from a video file
     * @param videoFile The video file to generate thumbnail from
     * @return Path to the generated thumbnail
     */
    public String generateThumbnail(MultipartFile videoFile) {
        try {
            // Create a temporary file for processing
            Path tempVideo = Files.createTempFile("video-", "." + getFileExtension(videoFile.getOriginalFilename()));
            videoFile.transferTo(tempVideo);

            // Get video duration and dimensions
            MultimediaObject multimediaObject = new MultimediaObject(tempVideo.toFile());
            MultimediaInfo info = multimediaObject.getInfo();
            VideoSize size = info.getVideo().getSize();
            
            // Calculate thumbnail dimensions maintaining aspect ratio
            int width = 320;
            int height = (int) (width * ((double) size.getHeight() / size.getWidth()));
            
            // Generate thumbnail at 10% of the video duration
            long duration = info.getDuration() / 1000; // in seconds
            long frameAt = duration > 10 ? 10 : duration / 2; // Use 10th second or middle if shorter
            
            // Extract frame using FFmpeg (handled by JAVE library)
            String thumbnailPath = "thumbnails/" + UUID.randomUUID() + ".jpg";
            
            // Save the thumbnail
            ByteArrayOutputStream thumbnailStream = new ByteArrayOutputStream();
            Thumbnails.of(tempVideo.toFile())
                    .size(width, height)
                    .outputFormat("jpg")
                    .toOutputStream(thumbnailStream);
            
            // Store the thumbnail
            try (InputStream inputStream = new ByteArrayInputStream(thumbnailStream.toByteArray())) {
                return fileStorageService.storeFile(
                    new com.reelsnack.reelsnack.util.ByteArrayMultipartFile(
                        "thumbnail",
                        "thumbnail.jpg",
                        "image/jpeg",
                        thumbnailStream.toByteArray()
                    ),
                    "thumbnails"
                );
            }
            
        } catch (IOException | EncoderException e) {
            log.error("Error generating thumbnail: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Get video duration in seconds
     */
    public long getVideoDuration(Path videoPath) throws IOException, EncoderException {
        MultimediaObject multimediaObject = new MultimediaObject(videoPath.toFile());
        return multimediaObject.getInfo().getDuration() / 1000; // Convert to seconds
    }
    
    /**
     * Get video dimensions
     */
    public VideoSize getVideoDimensions(Path videoPath) throws IOException, EncoderException {
        MultimediaObject multimediaObject = new MultimediaObject(videoPath.toFile());
        return multimediaObject.getInfo().getVideo().getSize();
    }
    
    /**
     * Get file extension from filename
     */
    private String getFileExtension(String filename) {
        if (filename == null) {
            return "";
        }
        int lastDot = filename.lastIndexOf('.');
        return lastDot == -1 ? "" : filename.substring(lastDot + 1);
    }
    
    /**
     * Process video file (generate thumbnail, extract metadata, etc.)
     */
    public VideoProcessingResult processVideo(MultipartFile videoFile) {
        try {
            // Generate thumbnail
            String thumbnailUrl = generateThumbnail(videoFile);
            
            // Get video metadata
            Path tempVideo = null;
            try {
                tempVideo = Files.createTempFile("video-", "." + getFileExtension(videoFile.getOriginalFilename()));
                videoFile.transferTo(tempVideo);
                
                // Get video duration and dimensions
                long duration = getVideoDuration(tempVideo);
                VideoSize dimensions = getVideoDimensions(tempVideo);
                
                // Build and return result
                return VideoProcessingResult.builder()
                    .thumbnailUrl(thumbnailUrl)
                    .duration(duration)
                    .format(getFileExtension(videoFile.getOriginalFilename()))
                    .size(videoFile.getSize())
                    .width(dimensions != null ? dimensions.getWidth() : 0)
                    .height(dimensions != null ? dimensions.getHeight() : 0)
                    .build();
            } catch (IOException e) {
                log.error("Error processing video: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to process video", e);
            } finally {
                // Clean up temporary files
                if (tempVideo != null) {
                    try {
                        Files.deleteIfExists(tempVideo);
                    } catch (IOException e) {
                        log.warn("Failed to delete temporary video file: {}", tempVideo, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing video: {}", e.getMessage(), e);
            throw new RuntimeException("Error processing video: " + e.getMessage(), e);
        }
    }
    
    @Data
    @Builder
    public static class VideoProcessingResult {
        private String thumbnailUrl;
        private long duration; // in seconds
        private String format;
        private long size; // in bytes
        private int width;
        private int height;
    }
}
