package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.MenuRequest;
import com.reelsnack.reelsnack.dto.response.MenuResponse;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.UnauthorizedException;
import com.reelsnack.reelsnack.mapper.MenuMapper;
import com.reelsnack.reelsnack.model.*;
import com.reelsnack.reelsnack.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * Service class for managing restaurant menus, categories, and items.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class MenuManagementService {

    private final RestaurantRepository restaurantRepository;
    private final MenuCategoryRepository menuCategoryRepository;
    private final MenuItemRepository menuItemRepository;
    private final MenuItemOptionRepository menuItemOptionRepository;
    private final MenuItemOptionValueRepository menuItemOptionValueRepository;
    private final UserService userService;
    private final FileStorageService fileStorageService;
    private final ThumbnailGenerationService thumbnailGenerationService;
    private final MenuMapper menuMapper;

    // ===== MENU CATEGORY OPERATIONS =====

    /**
     * Create a new menu category
     */
    @Transactional
    public MenuResponse.CategoryResponse createCategory(Long restaurantId, MenuRequest.CreateCategoryRequest request, String username) {
        log.info("Creating menu category for restaurant ID: {}", restaurantId);
        
        Restaurant restaurant = getRestaurantAndValidateOwnership(restaurantId, username);
        
        // Check if category name already exists
        if (menuCategoryRepository.existsByRestaurantAndNameIgnoreCase(restaurant, request.getName())) {
            throw new IllegalArgumentException("Category name already exists in this restaurant");
        }
        
        MenuCategory category = menuMapper.toEntity(request);
        category.setRestaurant(restaurant);
        
        // Set sort order if not provided
        if (category.getSortOrder() == null || category.getSortOrder() == 0) {
            category.setSortOrder(menuCategoryRepository.getNextSortOrder(restaurant));
        }
        
        MenuCategory savedCategory = menuCategoryRepository.save(category);
        log.info("Created menu category with ID: {} for restaurant: {}", savedCategory.getId(), restaurantId);
        
        return menuMapper.toCategoryResponse(savedCategory);
    }

    /**
     * Update menu category
     */
    @Transactional
    public MenuResponse.CategoryResponse updateCategory(Long categoryId, MenuRequest.UpdateCategoryRequest request, String username) {
        log.info("Updating menu category ID: {}", categoryId);
        
        MenuCategory category = getCategoryById(categoryId);
        validateRestaurantOwnership(category.getRestaurant(), username);
        
        // Check name uniqueness if name is being changed
        if (!category.getName().equalsIgnoreCase(request.getName()) &&
            menuCategoryRepository.existsByRestaurantAndNameIgnoreCaseAndIdNot(
                category.getRestaurant(), request.getName(), categoryId)) {
            throw new IllegalArgumentException("Category name already exists in this restaurant");
        }
        
        menuMapper.updateCategoryFromRequest(category, request);
        MenuCategory updatedCategory = menuCategoryRepository.save(category);
        
        log.info("Updated menu category ID: {}", categoryId);
        return menuMapper.toCategoryResponse(updatedCategory);
    }

    /**
     * Get menu categories for restaurant
     */
    public List<MenuResponse.CategoryResponse> getRestaurantCategories(Long restaurantId, boolean activeOnly) {
        List<MenuCategory> categories;
        if (activeOnly) {
            categories = menuCategoryRepository.findByRestaurantIdAndIsActiveTrueOrderBySortOrderAscNameAsc(restaurantId);
        } else {
            categories = menuCategoryRepository.findByRestaurantIdOrderBySortOrderAscNameAsc(restaurantId);
        }
        return menuMapper.toCategoryResponseList(categories);
    }

    /**
     * Delete menu category
     */
    @Transactional
    public void deleteCategory(Long categoryId, String username) {
        log.info("Deleting menu category ID: {}", categoryId);
        
        MenuCategory category = getCategoryById(categoryId);
        validateRestaurantOwnership(category.getRestaurant(), username);
        
        menuCategoryRepository.delete(category);
        log.info("Deleted menu category ID: {}", categoryId);
    }

    // ===== MENU ITEM OPERATIONS =====

    /**
     * Create a new menu item
     */
    @Transactional
    public MenuResponse.ItemResponse createMenuItem(Long categoryId, MenuRequest.CreateItemRequest request, String username) {
        log.info("Creating menu item for category ID: {}", categoryId);
        
        MenuCategory category = getCategoryById(categoryId);
        validateRestaurantOwnership(category.getRestaurant(), username);
        
        // Check if item name already exists in category
        if (menuItemRepository.existsByCategoryAndNameIgnoreCase(category, request.getName())) {
            throw new IllegalArgumentException("Item name already exists in this category");
        }
        
        MenuItem item = menuMapper.toEntity(request);
        item.setCategory(category);
        
        // Set sort order if not provided
        if (item.getSortOrder() == null || item.getSortOrder() == 0) {
            item.setSortOrder(menuItemRepository.getNextSortOrder(category));
        }
        
        MenuItem savedItem = menuItemRepository.save(item);
        log.info("Created menu item with ID: {} for category: {}", savedItem.getId(), categoryId);
        
        return menuMapper.toItemResponse(savedItem);
    }

    /**
     * Update menu item
     */
    @Transactional
    public MenuResponse.ItemResponse updateMenuItem(Long itemId, MenuRequest.UpdateItemRequest request, String username) {
        log.info("Updating menu item ID: {}", itemId);
        
        MenuItem item = getMenuItemById(itemId);
        validateRestaurantOwnership(item.getRestaurant(), username);
        
        // Check name uniqueness if name is being changed
        if (!item.getName().equalsIgnoreCase(request.getName()) &&
            menuItemRepository.existsByCategoryAndNameIgnoreCaseAndIdNot(
                item.getCategory(), request.getName(), itemId)) {
            throw new IllegalArgumentException("Item name already exists in this category");
        }
        
        menuMapper.updateItemFromRequest(item, request);
        MenuItem updatedItem = menuItemRepository.save(item);
        
        log.info("Updated menu item ID: {}", itemId);
        return menuMapper.toItemResponse(updatedItem);
    }

    /**
     * Upload menu item image
     */
    @Transactional
    public MenuResponse.ItemResponse uploadItemImage(Long itemId, MultipartFile imageFile, String username) {
        log.info("Uploading image for menu item ID: {}", itemId);
        
        MenuItem item = getMenuItemById(itemId);
        validateRestaurantOwnership(item.getRestaurant(), username);
        
        try {
            String imageUrl = fileStorageService.storeFile(imageFile, "menu/items");
            item.setImageUrl(imageUrl);
            
            MenuItem updatedItem = menuItemRepository.save(item);
            log.info("Updated image for menu item ID: {}", itemId);
            
            return menuMapper.toItemResponse(updatedItem);
        } catch (Exception e) {
            log.error("Error uploading image for menu item ID: {}", itemId, e);
            throw new RuntimeException("Failed to upload image: " + e.getMessage());
        }
    }

    /**
     * Get menu items for category
     */
    public List<MenuResponse.ItemResponse> getCategoryItems(Long categoryId, boolean availableOnly) {
        List<MenuItem> items;
        if (availableOnly) {
            items = menuItemRepository.findByCategoryIdAndIsAvailableTrueOrderBySortOrderAscNameAsc(categoryId);
        } else {
            items = menuItemRepository.findByCategoryIdOrderBySortOrderAscNameAsc(categoryId);
        }
        return menuMapper.toItemResponseList(items);
    }

    /**
     * Get menu items for restaurant
     */
    public Page<MenuResponse.ItemResponse> getRestaurantItems(Long restaurantId, boolean availableOnly, Pageable pageable) {
        Restaurant restaurant = getRestaurantById(restaurantId);
        Page<MenuItem> items;
        if (availableOnly) {
            items = menuItemRepository.findAvailableByRestaurant(restaurant, pageable);
        } else {
            items = menuItemRepository.findByRestaurant(restaurant, pageable);
        }
        return items.map(menuMapper::toItemResponse);
    }

    /**
     * Search menu items
     */
    public Page<MenuResponse.ItemResponse> searchMenuItems(Long restaurantId, String query, Pageable pageable) {
        Page<MenuItem> items = menuItemRepository.findByRestaurantIdAndNameContainingIgnoreCase(
            restaurantId, query, pageable);
        return items.map(menuMapper::toItemResponse);
    }

    /**
     * Get featured menu items
     */
    public List<MenuResponse.ItemResponse> getFeaturedItems(Long restaurantId) {
        List<MenuItem> items = menuItemRepository.findFeaturedByRestaurantId(restaurantId);
        return menuMapper.toItemResponseList(items);
    }

    /**
     * Update menu item rating
     */
    @Transactional
    public void updateMenuItemRating(Long itemId, BigDecimal newRating) {
        MenuItem item = getMenuItemById(itemId);
        item.updateRating(newRating);
        menuItemRepository.save(item);
        log.info("Updated rating for menu item ID: {} to: {}", itemId, item.getAverageRating());
    }

    /**
     * Increment menu item order count
     */
    @Transactional
    public void incrementItemOrderCount(Long itemId) {
        MenuItem item = getMenuItemById(itemId);
        item.incrementOrderCount();
        menuItemRepository.save(item);
        log.info("Incremented order count for menu item ID: {} to: {}", itemId, item.getTotalOrders());
    }

    /**
     * Toggle menu item availability
     */
    @Transactional
    public MenuResponse.ItemResponse toggleItemAvailability(Long itemId, String username) {
        log.info("Toggling availability for menu item ID: {}", itemId);
        
        MenuItem item = getMenuItemById(itemId);
        validateRestaurantOwnership(item.getRestaurant(), username);
        
        item.setIsAvailable(!item.getIsAvailable());
        MenuItem updatedItem = menuItemRepository.save(item);
        
        log.info("Toggled availability for menu item ID: {} to: {}", itemId, item.getIsAvailable());
        return menuMapper.toItemResponse(updatedItem);
    }

    /**
     * Delete menu item
     */
    @Transactional
    public void deleteMenuItem(Long itemId, String username) {
        log.info("Deleting menu item ID: {}", itemId);
        
        MenuItem item = getMenuItemById(itemId);
        validateRestaurantOwnership(item.getRestaurant(), username);
        
        menuItemRepository.delete(item);
        log.info("Deleted menu item ID: {}", itemId);
    }

    // Helper methods

    private Restaurant getRestaurantById(Long restaurantId) {
        return restaurantRepository.findById(restaurantId)
            .orElseThrow(() -> new ResourceNotFoundException("Restaurant not found with ID: " + restaurantId));
    }

    private Restaurant getRestaurantAndValidateOwnership(Long restaurantId, String username) {
        Restaurant restaurant = getRestaurantById(restaurantId);
        validateRestaurantOwnership(restaurant, username);
        return restaurant;
    }

    private void validateRestaurantOwnership(Restaurant restaurant, String username) {
        if (!restaurant.getOwner().getUsername().equals(username)) {
            throw new UnauthorizedException("You are not authorized to modify this restaurant's menu");
        }
    }

    private MenuCategory getCategoryById(Long categoryId) {
        return menuCategoryRepository.findById(categoryId)
            .orElseThrow(() -> new ResourceNotFoundException("Menu category not found with ID: " + categoryId));
    }

    private MenuItem getMenuItemById(Long itemId) {
        return menuItemRepository.findById(itemId)
            .orElseThrow(() -> new ResourceNotFoundException("Menu item not found with ID: " + itemId));
    }
}
