package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.config.properties.ContentProperties;
import com.reelsnack.reelsnack.util.ByteArrayMultipartFile;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;
import ws.schild.jave.encode.VideoAttributes;
import ws.schild.jave.info.MultimediaInfo;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Enhanced service for generating thumbnails from various media types.
 * Supports videos, images, and provides fallback thumbnails.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThumbnailGenerationService {

    private final FileStorageService fileStorageService;
    private final ContentProperties contentProperties;
    private final FallbackThumbnailService fallbackThumbnailService;

    // Supported formats
    private static final List<String> SUPPORTED_VIDEO_FORMATS = Arrays.asList(
        "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "m4v"
    );
    
    private static final List<String> SUPPORTED_IMAGE_FORMATS = Arrays.asList(
        "jpg", "jpeg", "png", "gif", "bmp", "webp"
    );

    // Thumbnail dimensions
    private static final int THUMBNAIL_WIDTH = 320;
    private static final int THUMBNAIL_HEIGHT = 240;
    private static final String THUMBNAIL_FORMAT = "jpg";

    /**
     * Generate thumbnail for any media file with robust error handling.
     * 
     * @param mediaFile The media file to generate thumbnail from
     * @param contentType The type of content (for fallback selection)
     * @return URL of the generated thumbnail or fallback thumbnail
     */
    public String generateThumbnail(MultipartFile mediaFile, String contentType) {
        if (mediaFile == null || mediaFile.isEmpty()) {
            log.warn("Media file is null or empty, using fallback thumbnail");
            return getFallbackThumbnail(contentType);
        }

        String originalFilename = mediaFile.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        
        log.debug("Generating thumbnail for file: {} ({})", originalFilename, fileExtension);

        try {
            // Determine media type and generate appropriate thumbnail
            if (isVideoFile(fileExtension)) {
                return generateVideoThumbnail(mediaFile);
            } else if (isImageFile(fileExtension)) {
                return generateImageThumbnail(mediaFile);
            } else {
                log.warn("Unsupported file format: {}, using fallback thumbnail", fileExtension);
                return getFallbackThumbnail(contentType);
            }
        } catch (Exception e) {
            log.error("Failed to generate thumbnail for {}: {}", originalFilename, e.getMessage(), e);
            return getFallbackThumbnail(contentType);
        }
    }

    /**
     * Generate thumbnail from video file using frame extraction.
     */
    private String generateVideoThumbnail(MultipartFile videoFile) throws IOException {
        Path tempVideoPath = null;
        Path tempThumbnailPath = null;
        
        try {
            // Create temporary files
            String fileExtension = getFileExtension(videoFile.getOriginalFilename());
            tempVideoPath = Files.createTempFile("video-", "." + fileExtension);
            tempThumbnailPath = Files.createTempFile("thumbnail-", ".jpg");
            
            // Save video to temporary file
            videoFile.transferTo(tempVideoPath);
            
            // Extract frame using JAVE
            boolean frameExtracted = extractVideoFrame(tempVideoPath, tempThumbnailPath);
            
            if (frameExtracted && Files.exists(tempThumbnailPath) && Files.size(tempThumbnailPath) > 0) {
                // Resize and optimize the extracted frame
                byte[] thumbnailBytes = optimizeThumbnail(tempThumbnailPath);
                
                // Store the thumbnail
                ByteArrayMultipartFile thumbnailFile = new ByteArrayMultipartFile(
                    "thumbnail",
                    "thumbnail.jpg",
                    "image/jpeg",
                    thumbnailBytes
                );
                
                String thumbnailUrl = fileStorageService.storeFile(thumbnailFile, "thumbnails");
                log.debug("Successfully generated video thumbnail: {}", thumbnailUrl);
                return thumbnailUrl;
            } else {
                throw new IOException("Frame extraction failed or produced empty file");
            }
            
        } catch (Exception e) {
            log.error("Error generating video thumbnail: {}", e.getMessage(), e);
            throw new IOException("Video thumbnail generation failed", e);
        } finally {
            // Clean up temporary files
            cleanupTempFile(tempVideoPath);
            cleanupTempFile(tempThumbnailPath);
        }
    }

    /**
     * Extract a frame from video at optimal position.
     */
    private boolean extractVideoFrame(Path videoPath, Path outputPath) {
        try {
            MultimediaObject source = new MultimediaObject(videoPath.toFile());
            MultimediaInfo info = source.getInfo();
            
            // Calculate optimal frame position (10% into video, but not less than 2 seconds)
            long durationMs = info.getDuration();
            long frameTimeMs = Math.max(2000, durationMs / 10); // 10% or 2 seconds, whichever is greater
            
            // Set up encoding attributes for frame extraction
            VideoAttributes videoAttribs = new VideoAttributes();
            videoAttribs.setCodec("mjpeg");
            videoAttribs.setSize(new ws.schild.jave.info.VideoSize(THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT));
            videoAttribs.setFrameRate(1); // Extract single frame
            
            EncodingAttributes encodingAttribs = new EncodingAttributes();
            encodingAttribs.setInputFormat("auto");
            encodingAttribs.setOutputFormat("mjpeg");
            encodingAttribs.setVideoAttributes(videoAttribs);
            encodingAttribs.setOffset((float) frameTimeMs / 1000); // Convert to seconds
            encodingAttribs.setDuration(0.1f); // Extract very short duration
            
            // Extract frame
            Encoder encoder = new Encoder();
            encoder.encode(source, outputPath.toFile(), encodingAttribs);
            
            return Files.exists(outputPath) && Files.size(outputPath) > 0;
            
        } catch (EncoderException | IOException e) {
            log.error("Frame extraction failed: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Generate thumbnail from image file with resizing and optimization.
     */
    private String generateImageThumbnail(MultipartFile imageFile) throws IOException {
        try {
            // Read the image
            BufferedImage originalImage = ImageIO.read(imageFile.getInputStream());
            if (originalImage == null) {
                throw new IOException("Could not read image file");
            }
            
            // Generate optimized thumbnail
            byte[] thumbnailBytes = createOptimizedThumbnail(originalImage);
            
            // Store the thumbnail
            ByteArrayMultipartFile thumbnailFile = new ByteArrayMultipartFile(
                "thumbnail",
                "thumbnail.jpg",
                "image/jpeg",
                thumbnailBytes
            );
            
            String thumbnailUrl = fileStorageService.storeFile(thumbnailFile, "thumbnails");
            log.debug("Successfully generated image thumbnail: {}", thumbnailUrl);
            return thumbnailUrl;
            
        } catch (Exception e) {
            log.error("Error generating image thumbnail: {}", e.getMessage(), e);
            throw new IOException("Image thumbnail generation failed", e);
        }
    }

    /**
     * Create optimized thumbnail from BufferedImage.
     */
    private byte[] createOptimizedThumbnail(BufferedImage originalImage) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        Thumbnails.of(originalImage)
            .size(THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
            .outputFormat(THUMBNAIL_FORMAT)
            .outputQuality(0.8) // Good quality with reasonable file size
            .toOutputStream(outputStream);
            
        return outputStream.toByteArray();
    }

    /**
     * Optimize thumbnail from file path.
     */
    private byte[] optimizeThumbnail(Path imagePath) throws IOException {
        BufferedImage image = ImageIO.read(imagePath.toFile());
        if (image == null) {
            throw new IOException("Could not read extracted frame");
        }
        return createOptimizedThumbnail(image);
    }

    /**
     * Get fallback thumbnail based on content type using FallbackThumbnailService.
     */
    private String getFallbackThumbnail(String contentType) {
        try {
            return fallbackThumbnailService.getFallbackThumbnailUrl(contentType);
        } catch (Exception e) {
            log.error("Error getting fallback thumbnail: {}", e.getMessage(), e);
            return "thumbnails/default.jpg"; // Ultimate fallback
        }
    }

    /**
     * Check if file is a supported video format.
     */
    private boolean isVideoFile(String extension) {
        return extension != null && SUPPORTED_VIDEO_FORMATS.contains(extension.toLowerCase());
    }

    /**
     * Check if file is a supported image format.
     */
    private boolean isImageFile(String extension) {
        return extension != null && SUPPORTED_IMAGE_FORMATS.contains(extension.toLowerCase());
    }

    /**
     * Extract file extension from filename.
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDot = filename.lastIndexOf('.');
        return lastDot == -1 ? "" : filename.substring(lastDot + 1);
    }

    /**
     * Clean up temporary file safely.
     */
    private void cleanupTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException e) {
                log.warn("Failed to delete temporary file: {}", tempFile, e);
            }
        }
    }
}
