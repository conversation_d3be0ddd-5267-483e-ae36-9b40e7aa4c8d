package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.response.FollowStatsResponse;
import com.reelsnack.reelsnack.dto.response.UserFollowResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserFollow;
import com.reelsnack.reelsnack.repository.ContentRepository;
import com.reelsnack.reelsnack.repository.UserFollowRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class for managing user follow relationships.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UserFollowService {

    private final UserFollowRepository userFollowRepository;
    private final UserRepository userRepository;
    private final ContentRepository contentRepository;

    // ===== Follow/Unfollow Operations =====

    /**
     * Follow a user.
     * @param followerUsername Username of the user who wants to follow
     * @param followingUsername Username of the user to be followed
     * @return Follow relationship response
     */
    @Transactional
    public UserFollowResponse followUser(String followerUsername, String followingUsername) {
        log.debug("User {} attempting to follow {}", followerUsername, followingUsername);

        // Prevent self-following
        if (followerUsername.equals(followingUsername)) {
            throw new BadRequestException("Users cannot follow themselves");
        }

        // Find both users
        User follower = userRepository.findByUsername(followerUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followerUsername));

        User following = userRepository.findByUsername(followingUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followingUsername));

        // Check if already following
        if (userFollowRepository.existsByFollowerAndFollowing(follower, following)) {
            throw new BadRequestException("User " + followerUsername + " is already following " + followingUsername);
        }

        // Create follow relationship
        UserFollow userFollow = UserFollow.builder()
            .follower(follower)
            .following(following)
            .build();

        UserFollow savedFollow = userFollowRepository.save(userFollow);
        log.info("User {} successfully followed {}", followerUsername, followingUsername);

        return mapToUserFollowResponse(savedFollow, followerUsername);
    }

    /**
     * Unfollow a user.
     * @param followerUsername Username of the user who wants to unfollow
     * @param followingUsername Username of the user to be unfollowed
     */
    @Transactional
    public void unfollowUser(String followerUsername, String followingUsername) {
        log.debug("User {} attempting to unfollow {}", followerUsername, followingUsername);

        // Find both users
        User follower = userRepository.findByUsername(followerUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followerUsername));

        User following = userRepository.findByUsername(followingUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followingUsername));

        // Check if follow relationship exists
        if (!userFollowRepository.existsByFollowerAndFollowing(follower, following)) {
            throw new BadRequestException("User " + followerUsername + " is not following " + followingUsername);
        }

        // Delete follow relationship
        userFollowRepository.deleteByFollowerAndFollowing(follower, following);
        log.info("User {} successfully unfollowed {}", followerUsername, followingUsername);
    }

    /**
     * Toggle follow status (follow if not following, unfollow if following).
     * @param followerUsername Username of the user
     * @param followingUsername Username of the target user
     * @return Follow relationship response if followed, null if unfollowed
     */
    @Transactional
    public UserFollowResponse toggleFollow(String followerUsername, String followingUsername) {
        log.debug("User {} toggling follow status for {}", followerUsername, followingUsername);

        // Find both users
        User follower = userRepository.findByUsername(followerUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followerUsername));

        User following = userRepository.findByUsername(followingUsername)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", followingUsername));

        boolean isFollowing = userFollowRepository.existsByFollowerAndFollowing(follower, following);

        if (isFollowing) {
            unfollowUser(followerUsername, followingUsername);
            return null; // Unfollowed
        } else {
            return followUser(followerUsername, followingUsername); // Followed
        }
    }

    // ===== Query Operations =====

    /**
     * Get followers of a user.
     * @param username Username of the user whose followers to get
     * @param pageable Pagination information
     * @param currentUsername Current user's username for follow status
     * @return Page of followers
     */
    public Page<UserFollowResponse.UserSummaryResponse> getFollowers(String username, Pageable pageable, String currentUsername) {
        log.debug("Getting followers for user {}", username);

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<UserFollow> follows = userFollowRepository.findFollowersByUser(user, pageable);
        return follows.map(follow -> mapToUserSummary(follow.getFollower(), currentUsername));
    }

    /**
     * Get users that a user is following.
     * @param username Username of the user whose following list to get
     * @param pageable Pagination information
     * @param currentUsername Current user's username for follow status
     * @return Page of following users
     */
    public Page<UserFollowResponse.UserSummaryResponse> getFollowing(String username, Pageable pageable, String currentUsername) {
        log.debug("Getting following list for user {}", username);

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<UserFollow> follows = userFollowRepository.findFollowingByUser(user, pageable);
        return follows.map(follow -> mapToUserSummary(follow.getFollowing(), currentUsername));
    }

    /**
     * Get follow statistics for a user.
     * @param username Username of the user
     * @param currentUsername Current user's username for relationship status
     * @return Follow statistics
     */
    public FollowStatsResponse getFollowStats(String username, String currentUsername) {
        log.debug("Getting follow stats for user {}", username);

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        long followerCount = userFollowRepository.countByFollower(user);
        long followingCount = userFollowRepository.countByFollowing(user);
        long contentCount = contentRepository.countByCreatorAndIsPublicTrueAndIsDeletedFalse(user);

        // Check relationship with current user
        boolean isFollowedByCurrentUser = false;
        boolean followsCurrentUser = false;

        if (currentUsername != null && !currentUsername.equals(username)) {
            isFollowedByCurrentUser = userFollowRepository.existsByFollowerUsernameAndFollowingUsername(
                currentUsername, username);
            followsCurrentUser = userFollowRepository.existsByFollowerUsernameAndFollowingUsername(
                username, currentUsername);
        }

        // Calculate mutual followers count
        long mutualFollowersCount = 0L;
        if (currentUsername != null && !currentUsername.equals(username)) {
            User currentUser = userRepository.findByUsername(currentUsername).orElse(null);
            if (currentUser != null) {
                mutualFollowersCount = userFollowRepository.countMutualFollowers(user, currentUser);
            }
        }

        return FollowStatsResponse.builder()
            .userId(user.getId())
            .username(username)
            .followerCount(followerCount)
            .followingCount(followingCount)
            .contentCount(contentCount)
            .isFollowedByCurrentUser(isFollowedByCurrentUser)
            .followsCurrentUser(followsCurrentUser)
            .mutualFollowersCount(mutualFollowersCount)
            .build();
    }

    /**
     * Get suggested users to follow.
     * @param username Username of the user to get suggestions for
     * @param pageable Pagination information
     * @return Page of suggested users
     */
    public Page<UserFollowResponse.UserSummaryResponse> getSuggestedUsers(String username, Pageable pageable) {
        log.debug("Getting suggested users for {}", username);

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<User> suggestedUsers = userFollowRepository.findSuggestedUsers(user, pageable);
        return suggestedUsers.map(suggestedUser -> mapToUserSummary(suggestedUser, username));
    }

    /**
     * Get mutual followers between two users.
     * @param username1 Username of the first user
     * @param username2 Username of the second user
     * @param pageable Pagination information
     * @param currentUsername Current user's username for follow status
     * @return Page of mutual followers
     */
    public Page<UserFollowResponse.UserSummaryResponse> getMutualFollowers(String username1, String username2,
                                                                           Pageable pageable, String currentUsername) {
        log.debug("Getting mutual followers between {} and {}", username1, username2);

        User user1 = userRepository.findByUsername(username1)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username1));

        User user2 = userRepository.findByUsername(username2)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username2));

        Page<User> mutualFollowers = userFollowRepository.findMutualFollowers(user1, user2, pageable);
        return mutualFollowers.map(user -> mapToUserSummary(user, currentUsername));
    }

    /**
     * Check if user A follows user B.
     * @param followerUsername Username of potential follower
     * @param followingUsername Username of potential following
     * @return true if follow relationship exists
     */
    public boolean isFollowing(String followerUsername, String followingUsername) {
        return userFollowRepository.existsByFollowerUsernameAndFollowingUsername(
            followerUsername, followingUsername);
    }

    // ===== Helper Methods =====

    /**
     * Map UserFollow entity to UserFollowResponse DTO.
     */
    private UserFollowResponse mapToUserFollowResponse(UserFollow userFollow, String currentUsername) {
        return UserFollowResponse.builder()
            .id(userFollow.getId())
            .follower(mapToUserSummary(userFollow.getFollower(), currentUsername))
            .following(mapToUserSummary(userFollow.getFollowing(), currentUsername))
            .createdAt(userFollow.getCreatedAt())
            .build();
    }

    /**
     * Map User entity to UserSummaryResponse DTO.
     */
    private UserFollowResponse.UserSummaryResponse mapToUserSummary(User user, String currentUsername) {
        boolean isFollowedByCurrentUser = currentUsername != null && 
            !currentUsername.equals(user.getUsername()) &&
            userFollowRepository.existsByFollowerUsernameAndFollowingUsername(currentUsername, user.getUsername());

        long contentCount = contentRepository.countByCreatorAndIsPublicTrueAndIsDeletedFalse(user);

        return UserFollowResponse.UserSummaryResponse.builder()
            .id(user.getId())
            .username(user.getUsername())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .profilePictureUrl(user.getProfilePicture())
            .bio(user.getBio())
            .isVerified(user.isVerified())
            .role(user.getRole() != null ? user.getRole().name() : "USER")
            .followerCount(userFollowRepository.countFollowersByUsername(user.getUsername()))
            .followingCount(userFollowRepository.countFollowingByUsername(user.getUsername()))
            .contentCount(contentCount)
            .isFollowedByCurrentUser(isFollowedByCurrentUser)
            .build();
    }
}
