package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.BookmarkContentRequest;
import com.reelsnack.reelsnack.dto.request.ShareContentRequest;
import com.reelsnack.reelsnack.dto.response.BookmarkResponse;
import com.reelsnack.reelsnack.dto.response.ShareResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.model.*;
import com.reelsnack.reelsnack.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service for managing content social interactions (sharing and bookmarking).
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ContentSocialService {

    private final ContentRepository contentRepository;
    private final UserRepository userRepository;
    private final ContentShareRepository contentShareRepository;
    private final ContentBookmarkRepository contentBookmarkRepository;

    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    // ===== Content Sharing Operations =====

    /**
     * Share content.
     * @param contentId ID of the content to share
     * @param request Share request details
     * @param username Username of the user sharing
     * @return Share response
     */
    @Transactional
    public ShareResponse shareContent(Long contentId, ShareContentRequest request, String username) {
        log.debug("User {} sharing content {}", username, contentId);

        // Find content and user
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        // Check if content is public
        if (!Boolean.TRUE.equals(content.getIsPublic())) {
            throw new BadRequestException("Cannot share private content");
        }

        // Check if already shared (optional - allow multiple shares)
        boolean alreadyShared = contentShareRepository.existsByUserAndContent(user, content);
        if (alreadyShared) {
            log.debug("User {} has already shared content {}, creating new share record", username, contentId);
        }

        // Create share record
        ContentShare share = ContentShare.builder()
            .content(content)
            .user(user)
            .sharePlatform(request.getSharePlatform())
            .shareMessage(request.getShareMessage())
            .build();

        ContentShare savedShare = contentShareRepository.save(share);

        // Update content share count
        content.incrementShareCount();
        contentRepository.save(content);

        log.info("User {} successfully shared content {}", username, contentId);

        return mapToShareResponse(savedShare);
    }

    /**
     * Get shares for content.
     * @param contentId ID of the content
     * @param pageable Pagination information
     * @return Page of shares
     */
    public Page<ShareResponse> getContentShares(Long contentId, Pageable pageable) {
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        Page<ContentShare> shares = contentShareRepository.findByContent(content, pageable);
        return shares.map(this::mapToShareResponse);
    }

    /**
     * Get shares by user.
     * @param username Username of the user
     * @param pageable Pagination information
     * @return Page of shares
     */
    public Page<ShareResponse> getUserShares(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<ContentShare> shares = contentShareRepository.findByUser(user, pageable);
        return shares.map(this::mapToShareResponse);
    }

    // ===== Content Bookmarking Operations =====

    /**
     * Toggle bookmark status for content.
     * @param contentId ID of the content to bookmark/unbookmark
     * @param request Bookmark request details
     * @param username Username of the user
     * @return Bookmark response if bookmarked, null if unbookmarked
     */
    @Transactional
    public BookmarkResponse toggleBookmark(Long contentId, BookmarkContentRequest request, String username) {
        log.debug("User {} toggling bookmark for content {}", username, contentId);

        // Find content and user
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        // Check if already bookmarked
        boolean isBookmarked = contentBookmarkRepository.existsByUserAndContent(user, content);

        if (isBookmarked) {
            // Remove bookmark
            contentBookmarkRepository.deleteByUserAndContent(user, content);
            log.info("User {} removed bookmark for content {}", username, contentId);
            return null; // Unbookmarked
        } else {
            // Add bookmark
            ContentBookmark bookmark = ContentBookmark.builder()
                .content(content)
                .user(user)
                .bookmarkCollection(request.getBookmarkCollection())
                .notes(request.getNotes())
                .build();

            ContentBookmark savedBookmark = contentBookmarkRepository.save(bookmark);
            log.info("User {} bookmarked content {}", username, contentId);

            return mapToBookmarkResponse(savedBookmark);
        }
    }

    /**
     * Get user's bookmarks.
     * @param username Username of the user
     * @param collection Optional collection filter
     * @param pageable Pagination information
     * @return Page of bookmarks
     */
    public Page<BookmarkResponse> getUserBookmarks(String username, String collection, Pageable pageable) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<ContentBookmark> bookmarks;
        if (collection != null && !collection.trim().isEmpty()) {
            bookmarks = contentBookmarkRepository.findByUserAndBookmarkCollection(user, collection, pageable);
        } else {
            bookmarks = contentBookmarkRepository.findByUser(user, pageable);
        }

        return bookmarks.map(this::mapToBookmarkResponse);
    }

    /**
     * Get user's bookmark collections.
     * @param username Username of the user
     * @return List of collection names
     */
    public List<String> getUserBookmarkCollections(String username) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        return contentBookmarkRepository.findBookmarkCollectionsByUser(user);
    }

    /**
     * Search user's bookmarks.
     * @param username Username of the user
     * @param searchTerm Search term
     * @param pageable Pagination information
     * @return Page of matching bookmarks
     */
    public Page<BookmarkResponse> searchUserBookmarks(String username, String searchTerm, Pageable pageable) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        Page<ContentBookmark> bookmarks = contentBookmarkRepository.searchBookmarks(user, searchTerm, pageable);
        return bookmarks.map(this::mapToBookmarkResponse);
    }

    /**
     * Check if user has bookmarked content.
     * @param contentId ID of the content
     * @param username Username of the user
     * @return true if bookmarked
     */
    public boolean isContentBookmarked(Long contentId, String username) {
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        return contentBookmarkRepository.existsByUserAndContent(user, content);
    }

    // ===== Helper Methods =====

    private ShareResponse mapToShareResponse(ContentShare share) {
        String shareUrl = frontendUrl + "/content/" + share.getContentId();

        return ShareResponse.builder()
            .id(share.getId())
            .contentId(share.getContentId())
            .contentTitle(share.getContentTitle())
            .sharePlatform(share.getSharePlatform())
            .shareMessage(share.getShareMessage())
            .shareUrl(shareUrl)
            .sharedAt(share.getCreatedAt())
            .sharedBy(ShareResponse.UserSummaryResponse.builder()
                .id(share.getUser().getId())
                .username(share.getUser().getUsername())
                .firstName(share.getUser().getFirstName())
                .lastName(share.getUser().getLastName())
                .profilePictureUrl(share.getUser().getProfilePicture())
                .build())
            .build();
    }

    private BookmarkResponse mapToBookmarkResponse(ContentBookmark bookmark) {
        Content content = bookmark.getContent();
        User creator = content.getCreator();

        return BookmarkResponse.builder()
            .id(bookmark.getId())
            .contentId(bookmark.getContentId())
            .content(BookmarkResponse.ContentSummaryResponse.builder()
                .id(content.getId())
                .title(content.getTitle())
                .description(content.getDescription())
                .thumbnailUrl(content.getThumbnailUrl())
                .contentType(content.getContentType().name())
                .creator(BookmarkResponse.UserSummaryResponse.builder()
                    .id(creator.getId())
                    .username(creator.getUsername())
                    .firstName(creator.getFirstName())
                    .lastName(creator.getLastName())
                    .profilePictureUrl(creator.getProfilePicture())
                    .build())
                .duration(content.getDuration())
                .viewCount(content.getViewCount())
                .likeCount(content.getLikeCount())
                .build())
            .bookmarkCollection(bookmark.getBookmarkCollection())
            .notes(bookmark.getNotes())
            .bookmarkedAt(bookmark.getCreatedAt())
            .build();
    }
}
