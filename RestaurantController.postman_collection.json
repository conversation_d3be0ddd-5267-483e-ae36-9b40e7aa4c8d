{"info": {"name": "RestaurantController API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Restaurant", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sample Restaurant\",\n  \"address\": \"123 Main St\",\n  \"city\": \"Sample City\",\n  \"country\": \"Sample Country\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"1234567890\",\n  \"status\": \"ACTIVE\",\n  \"isOpen\": true,\n  \"isVerified\": true,\n  \"isFeatured\": false\n}"}, "url": {"raw": "/api/v1/restaurants", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants"]}}}, {"name": "Update Restaurant", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Restaurant Name\",\n  \"address\": \"456 Another St\",\n  \"city\": \"Updated City\",\n  \"country\": \"Updated Country\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"0987654321\",\n  \"status\": \"ACTIVE\",\n  \"isOpen\": true,\n  \"isVerified\": true,\n  \"isFeatured\": true\n}"}, "url": {"raw": "/api/v1/restaurants/{restaurantId}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants", "{restaurantId}"]}}}, {"name": "Get All Restaurants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "url": {"raw": "/api/v1/restaurants?page=0&size=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}}, {"name": "Get Restaurant by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "url": {"raw": "/api/v1/restaurants/{restaurantId}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants", "{restaurantId}"]}}}, {"name": "Upload Restaurant Logo", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "url": {"raw": "/api/v1/restaurants/{restaurantId}/logo", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants", "{restaurantId}", "logo"]}}}, {"name": "Upload Restaurant Cover Image", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Authorization", "value": "Bearer <your_jwt_token>"}], "url": {"raw": "/api/v1/restaurants/{restaurantId}/cover", "host": ["{{baseUrl}}"], "path": ["api", "v1", "restaurants", "{restaurantId}", "cover"]}}}]}