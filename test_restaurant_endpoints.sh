#!/bin/bash

# Restaurant Management API Test Script
# This script tests all restaurant management endpoints with proper authentication

set -e  # Exit on any error

# Configuration
BASE_URL="http://localhost:8080/api"
AUTH_URL="$BASE_URL/auth"
RESTAURANT_URL="$BASE_URL/restaurants"
MENU_URL="$BASE_URL/menus"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables for tokens and IDs
RESTAURANT_TOKEN=""
CHEF_TOKEN=""
RESTAURANT_ID=""
CHEF_ID=""
CATEGORY_ID=""
ITEM_ID=""

# Helper function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Helper function to make HTTP requests with error handling
make_request() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    local description=$5
    
    print_status "Testing: $description"
    
    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data" \
                "$url")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Authorization: Bearer $token" \
                "$url")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$url")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                "$url")
        fi
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        print_success "$description - HTTP $http_code"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        echo "$response_body"
    else
        print_error "$description - HTTP $http_code"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        return 1
    fi
}

# Function to extract value from JSON response
extract_json_value() {
    local json=$1
    local key=$2
    echo "$json" | jq -r ".$key" 2>/dev/null || echo ""
}

# 1. Create Restaurant Owner User
create_restaurant_user() {
    print_status "=== Creating Restaurant Owner User ==="
    
    local signup_data='{
        "username": "restaurant_owner",
        "email": "<EMAIL>",
        "password": "password123",
        "firstName": "Restaurant",
        "lastName": "Owner",
        "phoneNumber": "+**********",
        "roles": ["restaurant"]
    }'
    
    response=$(make_request "POST" "$AUTH_URL/signup" "$signup_data" "" "Create restaurant owner account")
    
    # Sign in to get token
    local signin_data='{
        "usernameOrEmail": "restaurant_owner",
        "password": "password123"
    }'
    
    response=$(make_request "POST" "$AUTH_URL/signin" "$signin_data" "" "Sign in restaurant owner")
    RESTAURANT_TOKEN=$(extract_json_value "$response" "accessToken")
    
    if [ -z "$RESTAURANT_TOKEN" ]; then
        print_error "Failed to get restaurant owner token"
        exit 1
    fi
    
    print_success "Restaurant owner token obtained: ${RESTAURANT_TOKEN:0:20}..."
}

# 2. Create Chef User
create_chef_user() {
    print_status "=== Creating Chef User ==="
    
    local signup_data='{
        "username": "chef_user",
        "email": "<EMAIL>",
        "password": "password123",
        "firstName": "Chef",
        "lastName": "User",
        "phoneNumber": "+**********",
        "roles": ["chef"]
    }'
    
    response=$(make_request "POST" "$AUTH_URL/signup" "$signup_data" "" "Create chef account")
    
    # Sign in to get token
    local signin_data='{
        "usernameOrEmail": "chef_user",
        "password": "password123"
    }'
    
    response=$(make_request "POST" "$AUTH_URL/signin" "$signin_data" "" "Sign in chef")
    CHEF_TOKEN=$(extract_json_value "$response" "accessToken")
    
    if [ -z "$CHEF_TOKEN" ]; then
        print_error "Failed to get chef token"
        exit 1
    fi
    
    print_success "Chef token obtained: ${CHEF_TOKEN:0:20}..."
}

# 3. Test Restaurant Creation
test_restaurant_creation() {
    print_status "=== Testing Restaurant Creation ==="
    
    local restaurant_data='{
        "name": "Test Restaurant",
        "description": "A test restaurant for API testing",
        "address": "123 Test Street",
        "city": "Test City",
        "state": "Test State",
        "postalCode": "12345",
        "country": "Test Country",
        "latitude": 40.7128,
        "longitude": -74.0060,
        "phoneNumber": "+**********",
        "email": "<EMAIL>",
        "website": "https://testrestaurant.com",
        "deliveryFee": 5.99,
        "minimumOrder": 15.00,
        "estimatedDeliveryTime": 30,
        "deliveryRadius": 5.0,
        "openingTime": "09:00:00",
        "closingTime": "22:00:00",
        "cuisineTypes": ["Italian", "Pizza"],
        "specialFeatures": ["Outdoor Seating", "Delivery"]
    }'
    
    response=$(make_request "POST" "$RESTAURANT_URL" "$restaurant_data" "$RESTAURANT_TOKEN" "Create restaurant")
    RESTAURANT_ID=$(extract_json_value "$response" "id")
    
    if [ -z "$RESTAURANT_ID" ]; then
        print_error "Failed to get restaurant ID"
        exit 1
    fi
    
    print_success "Restaurant created with ID: $RESTAURANT_ID"
}

# 4. Test Restaurant Retrieval
test_restaurant_retrieval() {
    print_status "=== Testing Restaurant Retrieval ==="
    
    make_request "GET" "$RESTAURANT_URL/$RESTAURANT_ID" "" "" "Get restaurant by ID"
    make_request "GET" "$RESTAURANT_URL/my-restaurant" "" "$RESTAURANT_TOKEN" "Get my restaurant"
    make_request "GET" "$RESTAURANT_URL?page=0&size=10" "" "" "Get all restaurants"
    make_request "GET" "$RESTAURANT_URL/search?query=Test&page=0&size=10" "" "" "Search restaurants"
    make_request "GET" "$RESTAURANT_URL/featured?page=0&size=10" "" "" "Get featured restaurants"
}

# 5. Test Menu Category Creation
test_menu_category_creation() {
    print_status "=== Testing Menu Category Creation ==="
    
    local category_data='{
        "name": "Appetizers",
        "description": "Delicious appetizers to start your meal",
        "sortOrder": 1,
        "isActive": true
    }'
    
    response=$(make_request "POST" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "$category_data" "$RESTAURANT_TOKEN" "Create menu category")
    CATEGORY_ID=$(extract_json_value "$response" "id")
    
    if [ -z "$CATEGORY_ID" ]; then
        print_error "Failed to get category ID"
        exit 1
    fi
    
    print_success "Menu category created with ID: $CATEGORY_ID"
}

# 6. Test Menu Item Creation
test_menu_item_creation() {
    print_status "=== Testing Menu Item Creation ==="
    
    local item_data='{
        "name": "Bruschetta",
        "description": "Fresh tomatoes, basil, and mozzarella on toasted bread",
        "price": 8.99,
        "originalPrice": 10.99,
        "isAvailable": true,
        "isFeatured": true,
        "isVegetarian": true,
        "isVegan": false,
        "isGlutenFree": false,
        "isSpicy": false,
        "spiceLevel": 0,
        "calories": 250,
        "preparationTime": 10,
        "sortOrder": 1,
        "ingredients": ["Tomatoes", "Basil", "Mozzarella", "Bread"],
        "allergens": ["Gluten", "Dairy"]
    }'
    
    response=$(make_request "POST" "$MENU_URL/categories/$CATEGORY_ID/items" "$item_data" "$RESTAURANT_TOKEN" "Create menu item")
    ITEM_ID=$(extract_json_value "$response" "id")
    
    if [ -z "$ITEM_ID" ]; then
        print_error "Failed to get item ID"
        exit 1
    fi
    
    print_success "Menu item created with ID: $ITEM_ID"
}

# 7. Test Chef Association
test_chef_association() {
    print_status "=== Testing Chef Association ==="
    
    # First, we need to get the chef's user ID
    # For this test, we'll assume chef ID is 2 (since restaurant owner is likely 1)
    CHEF_ID=2
    
    make_request "POST" "$RESTAURANT_URL/$RESTAURANT_ID/chefs/$CHEF_ID" "" "$RESTAURANT_TOKEN" "Add chef to restaurant"
    
    # Test chef access to restaurant
    make_request "GET" "$RESTAURANT_URL/$RESTAURANT_ID" "" "$CHEF_TOKEN" "Chef access restaurant"
}

# 8. Test Chef Menu Management
test_chef_menu_management() {
    print_status "=== Testing Chef Menu Management ==="
    
    # Test chef can create menu category
    local chef_category_data='{
        "name": "Main Courses",
        "description": "Hearty main courses",
        "sortOrder": 2,
        "isActive": true
    }'
    
    make_request "POST" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "$chef_category_data" "$CHEF_TOKEN" "Chef create menu category"
    
    # Test chef can update menu item
    local update_item_data='{
        "name": "Updated Bruschetta",
        "description": "Updated description",
        "price": 9.99,
        "isAvailable": true,
        "isFeatured": true,
        "isVegetarian": true,
        "isVegan": false,
        "isGlutenFree": false,
        "isSpicy": false,
        "spiceLevel": 0,
        "calories": 250,
        "preparationTime": 10,
        "sortOrder": 1,
        "ingredients": ["Tomatoes", "Basil", "Mozzarella", "Bread"],
        "allergens": ["Gluten", "Dairy"]
    }'
    
    make_request "PUT" "$MENU_URL/items/$ITEM_ID" "$update_item_data" "$CHEF_TOKEN" "Chef update menu item"
}

# 9. Test Menu Retrieval
test_menu_retrieval() {
    print_status "=== Testing Menu Retrieval ==="
    
    make_request "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "" "Get restaurant categories"
    make_request "GET" "$MENU_URL/categories/$CATEGORY_ID/items" "" "" "Get category items"
    make_request "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/items?page=0&size=10" "" "" "Get restaurant items"
    make_request "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/items/featured" "" "" "Get featured items"
}

# 10. Test Restaurant Status Management
test_restaurant_status_management() {
    print_status "=== Testing Restaurant Status Management ==="
    
    make_request "PATCH" "$RESTAURANT_URL/$RESTAURANT_ID/toggle-status" "" "$RESTAURANT_TOKEN" "Toggle restaurant status"
}

# Main execution
main() {
    print_status "Starting Restaurant Management API Tests"
    print_status "Base URL: $BASE_URL"
    echo
    
    # Check if server is running
    if ! curl -s "$BASE_URL/auth/health" > /dev/null 2>&1; then
        print_warning "Server might not be running. Continuing anyway..."
    fi
    
    # Run tests
    create_restaurant_user
    echo
    create_chef_user
    echo
    test_restaurant_creation
    echo
    test_restaurant_retrieval
    echo
    test_menu_category_creation
    echo
    test_menu_item_creation
    echo
    test_chef_association
    echo
    test_chef_menu_management
    echo
    test_menu_retrieval
    echo
    test_restaurant_status_management
    echo
    
    print_success "All tests completed!"
    print_status "Restaurant ID: $RESTAURANT_ID"
    print_status "Category ID: $CATEGORY_ID"
    print_status "Item ID: $ITEM_ID"
}

# Run main function
main "$@"
