#!/bin/bash

# Enhanced Restaurant Management API Test Script
# Tests core functionality with detailed logging and comprehensive output

set -e

# Configuration
BASE_URL="http://localhost:8080/api"
AUTH_URL="$BASE_URL/auth"
RESTAURANT_URL="$BASE_URL/restaurants"
MENU_URL="$BASE_URL/menus"

# Colors for better output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Log file
LOG_FILE="restaurant_api_test_$(date +%Y%m%d_%H%M%S).log"

echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║           🍽️  Restaurant Management API Test Suite           ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo -e "${CYAN}📍 Base URL: $BASE_URL${NC}"
echo -e "${CYAN}📝 Log File: $LOG_FILE${NC}"
echo -e "${CYAN}🕐 Started: $(date)${NC}"
echo

# Enhanced logging function
log_to_file() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Enhanced test function with detailed logging
test_endpoint() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    local description=$5
    local expected_status=${6:-"2xx"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    echo -e "${BLUE}┌─────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BLUE}│ Test #$TOTAL_TESTS: $description${NC}"
    echo -e "${BLUE}└─────────────────────────────────────────────────────────────┘${NC}"

    # Log test details
    log_to_file "=== Test #$TOTAL_TESTS: $description ==="
    log_to_file "Method: $method"
    log_to_file "URL: $url"

    # Display request details
    echo -e "${CYAN}📤 REQUEST DETAILS:${NC}"
    echo -e "   Method: ${YELLOW}$method${NC}"
    echo -e "   URL: ${YELLOW}$url${NC}"

    if [ -n "$token" ]; then
        echo -e "   Authorization: ${YELLOW}Bearer ${token:0:20}...${NC}"
        log_to_file "Authorization: Bearer ${token:0:20}..."
    else
        echo -e "   Authorization: ${YELLOW}None${NC}"
        log_to_file "Authorization: None"
    fi

    if [ -n "$data" ]; then
        echo -e "   Request Body:"
        echo "$data" | jq '.' 2>/dev/null || echo "$data"
        log_to_file "Request Body: $data"
    else
        echo -e "   Request Body: ${YELLOW}None${NC}"
        log_to_file "Request Body: None"
    fi

    echo

    # Make the request with timing
    start_time=$(date +%s.%N)

    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}\n%{time_total}" -X "$method" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\n%{http_code}\n%{time_total}" -X "$method" \
                -H "Authorization: Bearer $token" "$url")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}\n%{time_total}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\n%{http_code}\n%{time_total}" -X "$method" "$url")
        fi
    fi

    end_time=$(date +%s.%N)

    # Parse response
    http_code=$(echo "$response" | tail -n2 | head -n1)
    response_time=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d' | sed '$d')

    # Display response details
    echo -e "${CYAN}📥 RESPONSE DETAILS:${NC}"
    echo -e "   Status Code: ${YELLOW}$http_code${NC}"
    echo -e "   Response Time: ${YELLOW}${response_time}s${NC}"

    # Log response details
    log_to_file "Status Code: $http_code"
    log_to_file "Response Time: ${response_time}s"

    if [ -n "$body" ] && [ "$body" != "null" ] && [ "$body" != "" ]; then
        echo -e "   Response Body:"
        if echo "$body" | jq '.' >/dev/null 2>&1; then
            echo "$body" | jq '.'
            log_to_file "Response Body: $(echo "$body" | jq -c '.')"
        else
            echo "$body"
            log_to_file "Response Body: $body"
        fi
    else
        echo -e "   Response Body: ${YELLOW}Empty${NC}"
        log_to_file "Response Body: Empty"
    fi

    echo

    # Determine success/failure
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo -e "${GREEN}✅ TEST PASSED - HTTP $http_code${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_to_file "Result: PASSED"
        echo "$body"
    else
        echo -e "${RED}❌ TEST FAILED - HTTP $http_code${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_to_file "Result: FAILED"
        if [ "$expected_status" != "fail" ]; then
            return 1
        fi
    fi

    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo
    log_to_file ""
}

# Helper function to extract JSON values
extract_json_value() {
    local json=$1
    local key=$2
    echo "$json" | jq -r ".$key" 2>/dev/null || echo ""
}

# Function to display section header
section_header() {
    local title=$1
    local step=$2
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║  Step $step: $title${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    log_to_file "=== Step $step: $title ==="
}

# 1. Create Restaurant Owner
section_header "Create Restaurant Owner Account" "1"
SIGNUP_DATA='{
    "username": "restaurant_test",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Restaurant",
    "lastName": "Owner",
    "roles": ["restaurant"]
}'

response=$(test_endpoint "POST" "$AUTH_URL/signup" "$SIGNUP_DATA" "" "Create restaurant owner account")

# 2. Sign in Restaurant Owner
section_header "Authenticate Restaurant Owner" "2"
SIGNIN_DATA='{
    "usernameOrEmail": "restaurant_test",
    "password": "password123"
}'

response=$(test_endpoint "POST" "$AUTH_URL/signin" "$SIGNIN_DATA" "" "Sign in restaurant owner")
RESTAURANT_TOKEN=$(extract_json_value "$response" "accessToken")

if [ -z "$RESTAURANT_TOKEN" ] || [ "$RESTAURANT_TOKEN" = "null" ]; then
    echo -e "${RED}❌ Failed to get restaurant token${NC}"
    log_to_file "ERROR: Failed to get restaurant token"
    exit 1
fi

echo -e "${GREEN}🔑 Restaurant token obtained: ${RESTAURANT_TOKEN:0:30}...${NC}"
log_to_file "Restaurant token obtained successfully"

# 3. Create Chef
section_header "Create Chef Account" "3"
CHEF_SIGNUP_DATA='{
    "username": "chef_test",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Chef",
    "lastName": "User",
    "roles": ["chef"]
}'

response=$(test_endpoint "POST" "$AUTH_URL/signup" "$CHEF_SIGNUP_DATA" "" "Create chef account")

# 4. Sign in Chef
section_header "Authenticate Chef" "4"
CHEF_SIGNIN_DATA='{
    "usernameOrEmail": "chef_test",
    "password": "password123"
}'

response=$(test_endpoint "POST" "$AUTH_URL/signin" "$CHEF_SIGNIN_DATA" "" "Sign in chef")
CHEF_TOKEN=$(extract_json_value "$response" "accessToken")

if [ -z "$CHEF_TOKEN" ] || [ "$CHEF_TOKEN" = "null" ]; then
    echo -e "${RED}❌ Failed to get chef token${NC}"
    log_to_file "ERROR: Failed to get chef token"
    exit 1
fi

echo -e "${GREEN}🔑 Chef token obtained: ${CHEF_TOKEN:0:30}...${NC}"
log_to_file "Chef token obtained successfully"

# 5. Create Restaurant
section_header "Create Restaurant Profile" "5"
RESTAURANT_DATA='{
    "name": "Test Restaurant API",
    "description": "A comprehensive test restaurant for API testing with full feature set",
    "address": "123 Test Street",
    "city": "Test City",
    "state": "Test State",
    "postalCode": "12345",
    "country": "Test Country",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "phoneNumber": "+1234567892",
    "email": "<EMAIL>",
    "website": "https://testrestaurant.com",
    "deliveryFee": 5.99,
    "minimumOrder": 15.00,
    "estimatedDeliveryTime": 30,
    "deliveryRadius": 5.0,
    "cuisineTypes": ["Italian", "Pizza", "Mediterranean"],
    "specialFeatures": ["Outdoor Seating", "Delivery", "Takeout", "Vegan Options"]
}'

response=$(test_endpoint "POST" "$RESTAURANT_URL" "$RESTAURANT_DATA" "$RESTAURANT_TOKEN" "Create restaurant profile")
RESTAURANT_ID=$(extract_json_value "$response" "id")

if [ -z "$RESTAURANT_ID" ] || [ "$RESTAURANT_ID" = "null" ]; then
    echo -e "${RED}❌ Failed to get restaurant ID${NC}"
    log_to_file "ERROR: Failed to get restaurant ID"
    exit 1
fi

echo -e "${GREEN}🏪 Restaurant created with ID: $RESTAURANT_ID${NC}"
log_to_file "Restaurant created successfully with ID: $RESTAURANT_ID"

# 6. Test Restaurant Access
section_header "Test Restaurant Access Control" "6"
test_endpoint "GET" "$RESTAURANT_URL/$RESTAURANT_ID" "" "" "Get restaurant details (public access)"
test_endpoint "GET" "$RESTAURANT_URL/my-restaurant" "" "$RESTAURANT_TOKEN" "Get my restaurant (owner access)"
test_endpoint "GET" "$RESTAURANT_URL?page=0&size=5" "" "" "List restaurants (public access)"

# 7. Create Menu Category
section_header "Create Menu Category" "7"
CATEGORY_DATA='{
    "name": "Appetizers",
    "description": "Delicious appetizers to start your dining experience",
    "sortOrder": 1,
    "isActive": true
}'

response=$(test_endpoint "POST" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "$CATEGORY_DATA" "$RESTAURANT_TOKEN" "Create menu category")
CATEGORY_ID=$(extract_json_value "$response" "id")

if [ -z "$CATEGORY_ID" ] || [ "$CATEGORY_ID" = "null" ]; then
    echo -e "${RED}❌ Failed to get category ID${NC}"
    log_to_file "ERROR: Failed to get category ID"
    exit 1
fi

echo -e "${GREEN}📂 Category created with ID: $CATEGORY_ID${NC}"
log_to_file "Menu category created successfully with ID: $CATEGORY_ID"

# 8. Create Menu Item
section_header "Create Menu Item" "8"
ITEM_DATA='{
    "name": "Bruschetta Classica",
    "description": "Fresh Roma tomatoes, basil, garlic, and extra virgin olive oil on toasted artisan bread",
    "price": 8.99,
    "originalPrice": 10.99,
    "isAvailable": true,
    "isFeatured": true,
    "isVegetarian": true,
    "isVegan": false,
    "isGlutenFree": false,
    "isSpicy": false,
    "spiceLevel": 0,
    "calories": 250,
    "preparationTime": 10,
    "sortOrder": 1,
    "ingredients": ["Roma Tomatoes", "Fresh Basil", "Garlic", "Extra Virgin Olive Oil", "Artisan Bread"],
    "allergens": ["Gluten"],
    "nutritionalInfo": "Rich in vitamins and antioxidants"
}'

response=$(test_endpoint "POST" "$MENU_URL/categories/$CATEGORY_ID/items" "$ITEM_DATA" "$RESTAURANT_TOKEN" "Create menu item")
ITEM_ID=$(extract_json_value "$response" "id")

if [ -z "$ITEM_ID" ] || [ "$ITEM_ID" = "null" ]; then
    echo -e "${RED}❌ Failed to get item ID${NC}"
    log_to_file "ERROR: Failed to get item ID"
    exit 1
fi

echo -e "${GREEN}🍽️ Menu item created with ID: $ITEM_ID${NC}"
log_to_file "Menu item created successfully with ID: $ITEM_ID"

# 9. Test Chef Access (Should Fail - Not Associated Yet)
section_header "Test Chef Access (Unauthorized)" "9"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "$CHEF_TOKEN" "Chef access menu (should fail)" "fail"

# 10. Associate Chef with Restaurant
section_header "Associate Chef with Restaurant" "10"
# For testing, we'll use chef user ID 2 (assuming restaurant owner is ID 1)
CHEF_USER_ID=2
test_endpoint "POST" "$RESTAURANT_URL/$RESTAURANT_ID/chefs/$CHEF_USER_ID" "" "$RESTAURANT_TOKEN" "Add chef to restaurant"

# 11. Test Chef Access (Should Work Now)
section_header "Test Chef Access (Authorized)" "11"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "$CHEF_TOKEN" "Chef access menu (should work now)"
test_endpoint "PUT" "$MENU_URL/items/$ITEM_ID" '{
    "name": "Updated Bruschetta Classica",
    "description": "Updated by chef - Fresh Roma tomatoes with premium ingredients",
    "price": 9.99,
    "isAvailable": true,
    "isFeatured": true,
    "isVegetarian": true,
    "isVegan": false,
    "isGlutenFree": false,
    "isSpicy": false,
    "spiceLevel": 0,
    "calories": 250,
    "preparationTime": 12,
    "sortOrder": 1,
    "ingredients": ["Roma Tomatoes", "Fresh Basil", "Garlic", "Extra Virgin Olive Oil", "Artisan Bread"],
    "allergens": ["Gluten"]
}' "$CHEF_TOKEN" "Chef update menu item"

# 12. Test Menu Retrieval (Public Access)
section_header "Test Menu Retrieval (Public)" "12"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "" "Get restaurant categories (public)"
test_endpoint "GET" "$MENU_URL/categories/$CATEGORY_ID/items" "" "" "Get category items (public)"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/items/featured" "" "" "Get featured items (public)"

# 13. Test Restaurant Search & Discovery
section_header "Test Restaurant Search & Discovery" "13"
test_endpoint "GET" "$RESTAURANT_URL/search?query=Test&page=0&size=5" "" "" "Search restaurants by name"
test_endpoint "GET" "$RESTAURANT_URL/nearby?latitude=40.7128&longitude=-74.0060&page=0&size=5" "" "" "Find nearby restaurants"
test_endpoint "GET" "$RESTAURANT_URL/featured?page=0&size=5" "" "" "Get featured restaurants"
test_endpoint "GET" "$RESTAURANT_URL/top-rated?minReviews=0&page=0&size=5" "" "" "Get top-rated restaurants"

# 14. Test Restaurant Management
section_header "Test Restaurant Management" "14"
test_endpoint "PATCH" "$RESTAURANT_URL/$RESTAURANT_ID/toggle-status" "" "$RESTAURANT_TOKEN" "Toggle restaurant open/closed status"

# Generate comprehensive test summary
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                    🎯 TEST SUMMARY REPORT                    ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo
echo -e "${CYAN}📊 Test Statistics:${NC}"
echo -e "   Total Tests: ${YELLOW}$TOTAL_TESTS${NC}"
echo -e "   Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "   Failed: ${RED}$FAILED_TESTS${NC}"
echo -e "   Success Rate: ${YELLOW}$(( PASSED_TESTS * 100 / TOTAL_TESTS ))%${NC}"
echo
echo -e "${CYAN}🔑 Generated Resources:${NC}"
echo -e "   Restaurant ID: ${YELLOW}$RESTAURANT_ID${NC}"
echo -e "   Category ID: ${YELLOW}$CATEGORY_ID${NC}"
echo -e "   Menu Item ID: ${YELLOW}$ITEM_ID${NC}"
echo -e "   Restaurant Token: ${YELLOW}${RESTAURANT_TOKEN:0:30}...${NC}"
echo -e "   Chef Token: ${YELLOW}${CHEF_TOKEN:0:30}...${NC}"
echo
echo -e "${CYAN}✅ Features Tested Successfully:${NC}"
echo -e "   • User registration and authentication (Restaurant & Chef roles)"
echo -e "   • Restaurant profile creation and management"
echo -e "   • Menu category and item management"
echo -e "   • Role-based access control (Owner vs Chef vs Public)"
echo -e "   • Chef-restaurant association system"
echo -e "   • Restaurant search and discovery"
echo -e "   • Public API endpoints"
echo -e "   • Restaurant status management"
echo
echo -e "${CYAN}📝 Log File: ${YELLOW}$LOG_FILE${NC}"
echo -e "${CYAN}🕐 Completed: ${YELLOW}$(date)${NC}"
echo
if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! Restaurant Management System is working perfectly! 🎉${NC}"
else
    echo -e "${YELLOW}⚠️  Some tests failed. Check the log file for details.${NC}"
fi

log_to_file "=== TEST SUMMARY ==="
log_to_file "Total Tests: $TOTAL_TESTS"
log_to_file "Passed: $PASSED_TESTS"
log_to_file "Failed: $FAILED_TESTS"
log_to_file "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
log_to_file "Completed: $(date)"
