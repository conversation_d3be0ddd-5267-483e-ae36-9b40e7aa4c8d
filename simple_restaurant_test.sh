#!/bin/bash

# Simple Restaurant Management API Test Script
# Tests core functionality with proper authentication

set -e

# Configuration
BASE_URL="http://localhost:8080/api"
AUTH_URL="$BASE_URL/auth"
RESTAURANT_URL="$BASE_URL/restaurants"
MENU_URL="$BASE_URL/menus"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Restaurant Management API Test ===${NC}"
echo "Base URL: $BASE_URL"
echo

# Helper function
test_endpoint() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    local description=$5
    
    echo -e "${BLUE}Testing: $description${NC}"
    
    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Authorization: Bearer $token" "$url")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" "$url")
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
        fi
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [[ $http_code -ge 200 && $http_code -lt 300 ]]; then
        echo -e "${GREEN}✓ SUCCESS - HTTP $http_code${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        echo "$body"
    else
        echo -e "${RED}✗ FAILED - HTTP $http_code${NC}"
        echo "$body"
        return 1
    fi
}

# 1. Create Restaurant Owner
echo -e "${BLUE}=== Step 1: Create Restaurant Owner ===${NC}"
SIGNUP_DATA='{
    "username": "restaurant_test",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Restaurant",
    "lastName": "Owner",
    "roles": ["restaurant"]
}'

response=$(test_endpoint "POST" "$AUTH_URL/signup" "$SIGNUP_DATA" "" "Create restaurant owner")

# 2. Sign in Restaurant Owner
echo -e "${BLUE}=== Step 2: Sign in Restaurant Owner ===${NC}"
SIGNIN_DATA='{
    "usernameOrEmail": "restaurant_test",
    "password": "password123"
}'

response=$(test_endpoint "POST" "$AUTH_URL/signin" "$SIGNIN_DATA" "" "Sign in restaurant owner")
RESTAURANT_TOKEN=$(echo "$response" | jq -r '.accessToken' 2>/dev/null || echo "")

if [ -z "$RESTAURANT_TOKEN" ] || [ "$RESTAURANT_TOKEN" = "null" ]; then
    echo -e "${RED}Failed to get restaurant token${NC}"
    exit 1
fi

echo -e "${GREEN}Restaurant token obtained${NC}"
echo

# 3. Create Chef
echo -e "${BLUE}=== Step 3: Create Chef ===${NC}"
CHEF_SIGNUP_DATA='{
    "username": "chef_test",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Chef",
    "lastName": "User",
    "roles": ["chef"]
}'

response=$(test_endpoint "POST" "$AUTH_URL/signup" "$CHEF_SIGNUP_DATA" "" "Create chef")

# 4. Sign in Chef
echo -e "${BLUE}=== Step 4: Sign in Chef ===${NC}"
CHEF_SIGNIN_DATA='{
    "usernameOrEmail": "chef_test",
    "password": "password123"
}'

response=$(test_endpoint "POST" "$AUTH_URL/signin" "$CHEF_SIGNIN_DATA" "" "Sign in chef")
CHEF_TOKEN=$(echo "$response" | jq -r '.accessToken' 2>/dev/null || echo "")

if [ -z "$CHEF_TOKEN" ] || [ "$CHEF_TOKEN" = "null" ]; then
    echo -e "${RED}Failed to get chef token${NC}"
    exit 1
fi

echo -e "${GREEN}Chef token obtained${NC}"
echo

# 5. Create Restaurant
echo -e "${BLUE}=== Step 5: Create Restaurant ===${NC}"
RESTAURANT_DATA='{
    "name": "Test Restaurant API",
    "description": "A test restaurant for API testing",
    "address": "123 Test Street",
    "city": "Test City",
    "state": "Test State",
    "postalCode": "12345",
    "country": "Test Country",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "phoneNumber": "+1234567892",
    "email": "<EMAIL>",
    "deliveryFee": 5.99,
    "minimumOrder": 15.00,
    "estimatedDeliveryTime": 30,
    "deliveryRadius": 5.0,
    "cuisineTypes": ["Italian", "Pizza"],
    "specialFeatures": ["Outdoor Seating", "Delivery"]
}'

response=$(test_endpoint "POST" "$RESTAURANT_URL" "$RESTAURANT_DATA" "$RESTAURANT_TOKEN" "Create restaurant")
RESTAURANT_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$RESTAURANT_ID" ] || [ "$RESTAURANT_ID" = "null" ]; then
    echo -e "${RED}Failed to get restaurant ID${NC}"
    exit 1
fi

echo -e "${GREEN}Restaurant created with ID: $RESTAURANT_ID${NC}"
echo

# 6. Test Restaurant Access
echo -e "${BLUE}=== Step 6: Test Restaurant Access ===${NC}"
test_endpoint "GET" "$RESTAURANT_URL/$RESTAURANT_ID" "" "" "Get restaurant (public)"
test_endpoint "GET" "$RESTAURANT_URL/my-restaurant" "" "$RESTAURANT_TOKEN" "Get my restaurant (owner)"
echo

# 7. Create Menu Category
echo -e "${BLUE}=== Step 7: Create Menu Category ===${NC}"
CATEGORY_DATA='{
    "name": "Appetizers",
    "description": "Delicious appetizers",
    "sortOrder": 1,
    "isActive": true
}'

response=$(test_endpoint "POST" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "$CATEGORY_DATA" "$RESTAURANT_TOKEN" "Create menu category")
CATEGORY_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$CATEGORY_ID" ] || [ "$CATEGORY_ID" = "null" ]; then
    echo -e "${RED}Failed to get category ID${NC}"
    exit 1
fi

echo -e "${GREEN}Category created with ID: $CATEGORY_ID${NC}"
echo

# 8. Create Menu Item
echo -e "${BLUE}=== Step 8: Create Menu Item ===${NC}"
ITEM_DATA='{
    "name": "Bruschetta",
    "description": "Fresh tomatoes and basil",
    "price": 8.99,
    "isAvailable": true,
    "isFeatured": true,
    "isVegetarian": true,
    "preparationTime": 10,
    "ingredients": ["Tomatoes", "Basil", "Bread"],
    "allergens": ["Gluten"]
}'

response=$(test_endpoint "POST" "$MENU_URL/categories/$CATEGORY_ID/items" "$ITEM_DATA" "$RESTAURANT_TOKEN" "Create menu item")
ITEM_ID=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "")

if [ -z "$ITEM_ID" ] || [ "$ITEM_ID" = "null" ]; then
    echo -e "${RED}Failed to get item ID${NC}"
    exit 1
fi

echo -e "${GREEN}Menu item created with ID: $ITEM_ID${NC}"
echo

# 9. Test Chef Access (This will fail until chef is associated)
echo -e "${BLUE}=== Step 9: Test Chef Access (Should Fail) ===${NC}"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "$CHEF_TOKEN" "Chef access menu (should fail)" || echo -e "${BLUE}Expected failure - chef not associated yet${NC}"
echo

# 10. Associate Chef with Restaurant
echo -e "${BLUE}=== Step 10: Associate Chef with Restaurant ===${NC}"
# We need to get the chef's user ID first - for testing, we'll use ID 2
CHEF_USER_ID=2
test_endpoint "POST" "$RESTAURANT_URL/$RESTAURANT_ID/chefs/$CHEF_USER_ID" "" "$RESTAURANT_TOKEN" "Add chef to restaurant"
echo

# 11. Test Chef Access (Should work now)
echo -e "${BLUE}=== Step 11: Test Chef Access (Should Work) ===${NC}"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "$CHEF_TOKEN" "Chef access menu (should work)"
echo

# 12. Test Menu Retrieval
echo -e "${BLUE}=== Step 12: Test Menu Retrieval ===${NC}"
test_endpoint "GET" "$MENU_URL/restaurants/$RESTAURANT_ID/categories" "" "" "Get restaurant categories (public)"
test_endpoint "GET" "$MENU_URL/categories/$CATEGORY_ID/items" "" "" "Get category items (public)"
echo

# 13. Test Restaurant Search
echo -e "${BLUE}=== Step 13: Test Restaurant Search ===${NC}"
test_endpoint "GET" "$RESTAURANT_URL/search?query=Test&page=0&size=10" "" "" "Search restaurants"
test_endpoint "GET" "$RESTAURANT_URL?page=0&size=10" "" "" "Get all restaurants"
echo

echo -e "${GREEN}=== All Tests Completed! ===${NC}"
echo "Restaurant ID: $RESTAURANT_ID"
echo "Category ID: $CATEGORY_ID"
echo "Item ID: $ITEM_ID"
echo
echo -e "${BLUE}Summary:${NC}"
echo "✓ Restaurant owner created and authenticated"
echo "✓ Chef created and authenticated"
echo "✓ Restaurant created successfully"
echo "✓ Menu category and item created"
echo "✓ Chef association tested"
echo "✓ Public endpoints tested"
